package plus.qdt.listener;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import plus.qdt.modules.order.order.client.ReceiptClient;
import plus.qdt.modules.order.order.entity.dos.Receipt;
import plus.qdt.routing.ReceiptRoutingKey;

/***
 * 发票创建通知
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReceiptMessageListener {

    private final ReceiptClient receiptClientl;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = ("${qdt.amqp.receipt}" + "_" + ReceiptRoutingKey.RECEIPT_CREATE)),
            exchange = @Exchange(value = "${qdt.amqp.receipt}"),
            key = ReceiptRoutingKey.RECEIPT_CREATE))
    public void onMessage(String receiptMessageJson) {
        Receipt receipt = JSONUtil.toBean(receiptMessageJson, Receipt.class);
        receiptClientl.saveReceipt(receipt);
    }
}
