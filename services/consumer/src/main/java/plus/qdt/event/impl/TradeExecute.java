package plus.qdt.event.impl;

import io.seata.spring.annotation.GlobalTransactional;
import plus.qdt.event.PaymentCallbackEvent;
import plus.qdt.modules.order.trade.client.TradeClient;
import plus.qdt.modules.payment.entity.dto.PaymentCallback;
import plus.qdt.modules.payment.entity.enums.PaymentSceneEnums;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 交易执行
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @since 2023/6/25 10:00
 */

@Component
@AllArgsConstructor
public class TradeExecute implements PaymentCallbackEvent {


    private final TradeClient tradeClient;

    @Override
    @GlobalTransactional
    public void paymentCallback(PaymentCallback paymentCallback) {
        tradeClient.paymentCallback(paymentCallback);
    }

    @Override
    public PaymentSceneEnums getScene() {
        return PaymentSceneEnums.TRADE;
    }
}
