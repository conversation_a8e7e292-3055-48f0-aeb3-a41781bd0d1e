package plus.qdt.modules.distribution.serviceimpl;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.vo.PageVO;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.distribution.entity.dos.Distribution;
import plus.qdt.modules.distribution.entity.dos.DistributionCash;
import plus.qdt.modules.distribution.entity.enums.DistributionStatusEnum;
import plus.qdt.modules.distribution.entity.vos.DistributionCashSearchParams;
import plus.qdt.modules.distribution.mapper.DistributionCashMapper;
import plus.qdt.modules.distribution.service.DistributionCashService;
import plus.qdt.modules.distribution.service.DistributionService;
import plus.qdt.modules.member.entity.dto.MemberWithdrawalMessage;
import plus.qdt.modules.payment.client.WalletClient;
import plus.qdt.modules.payment.entity.dto.UserWalletUpdateDTO;
import plus.qdt.modules.payment.entity.dto.UserWithdrawalMessage;
import plus.qdt.modules.payment.entity.enums.WalletServiceTypeEnum;
import plus.qdt.modules.payment.entity.enums.WithdrawStatusEnum;
import plus.qdt.mybatis.util.PageUtil;
import plus.qdt.routing.UserRoutingKey;
import plus.qdt.util.AmqpMessage;
import plus.qdt.util.AmqpSender;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * 分销佣金业务层实现
 *
 * <AUTHOR>
 * @since 2020-03-126 18:04:56
 */
@Service
@RequiredArgsConstructor
public class DistributionCashServiceImpl extends ServiceImpl<DistributionCashMapper, DistributionCash> implements DistributionCashService {
    /**
     * 分销员
     */
    private final DistributionService distributionService;
    /**
     * 用户钱包
     */
    private final WalletClient walletClient;

    private final AmqpSender amqpSender;

    private final AmqpExchangeProperties amqpExchangeProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cash(Double applyMoney) {

        //检查分销功能开关
        distributionService.checkDistributionSetting();

        //获取分销员
        Distribution distribution = distributionService.getDistribution();
        //如果未找到分销员或者分销员状态不是已通过则无法申请提现
        if (distribution != null && distribution.getDistributionStatus().equals(DistributionStatusEnum.PASS.name())) {
            //校验分销佣金是否大于提现金额
            if (distribution.getCanRebate() < applyMoney) {
                throw new ServiceException(ResultCode.WALLET_WITHDRAWAL_INSUFFICIENT);
            }
            //将提现金额存入冻结金额,扣减可提现金额
            distribution.setCanRebate(CurrencyUtil.sub(distribution.getCanRebate(), applyMoney));
            distribution.setCommissionFrozen(CurrencyUtil.add(distribution.getCommissionFrozen(), applyMoney));
            distributionService.updateById(distribution);
            //提现申请记录
            DistributionCash distributionCash = new DistributionCash("D" + SnowFlake.getId(), distribution.getId(), applyMoney, distribution.getMemberName());
            boolean result = this.save(distributionCash);
            if (result) {
                //发送提现消息
                UserWithdrawalMessage userWithdrawalMessage = new UserWithdrawalMessage();
                userWithdrawalMessage.setUserId(distribution.getMemberId());
                userWithdrawalMessage.setPrice(applyMoney);
                userWithdrawalMessage.setStatus(WithdrawStatusEnum.APPLY.name());
                amqpSender.send(
                        AmqpMessage.builder()
                                .exchange(amqpExchangeProperties.getUser())
                                .routingKey(UserRoutingKey.USER_WITHDRAWAL)
                                .message(userWithdrawalMessage)
                                .build()
                );
                return true;
            }
            return false;

        }
        throw new ServiceException(ResultCode.DISTRIBUTION_NOT_EXIST);

    }

    @Override
    public IPage<DistributionCash> getDistributionCash(PageVO page) {
        Distribution distribution = distributionService.getDistribution();
        QueryWrapper<DistributionCash> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("distribution_id", distribution.getId());
        return this.page(PageUtil.initPage(page), queryWrapper);
    }

    @Override
    public IPage<DistributionCash> getDistributionCash(DistributionCashSearchParams distributionCashSearchParams) {

        return this.page(PageUtil.initPage(distributionCashSearchParams), distributionCashSearchParams.queryWrapper());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DistributionCash audit(String id, String result) {

        //检查分销功能开关
        distributionService.checkDistributionSetting();

        //获取分销佣金信息
        DistributionCash distributorCash = this.getById(id);
        //只有分销员和分销佣金记录存在的情况才可以审核
        if (distributorCash == null) {
            throw new ServiceException(ResultCode.DISTRIBUTION_CASH_NOT_EXIST);
        }
        //获取分销员
        Distribution distribution = distributionService.getById(distributorCash.getDistributionId());
        if (distribution == null || !distribution.getDistributionStatus().equals(DistributionStatusEnum.PASS.name())) {
            throw new ServiceException(ResultCode.DISTRIBUTION_NOT_EXIST);
        }
        MemberWithdrawalMessage memberWithdrawalMessage = new MemberWithdrawalMessage();
        //审核通过
        if (result.equals(WithdrawStatusEnum.VIA_AUDITING.name())) {
            //分销记录操作
            distributorCash.setDistributionCashStatus(WithdrawStatusEnum.VIA_AUDITING.name());
            distributorCash.setPayTime(new Date());
            //提现到余额
            walletClient.increase(UserWalletUpdateDTO.builder()
                    .amount(distributorCash.getPrice())
                    .userId(distribution.getMemberId())
                    .detail("分销[" + distributorCash.getSn() + "]佣金提现到余额[" + distributorCash.getPrice() + "]")
                    .serviceType(WalletServiceTypeEnum.WALLET_COMMISSION)
                    .build());
            //扣减提提现金额
            distributionService.subCashRebate(distributorCash.getPrice(), distributorCash.getDistributionId());
            memberWithdrawalMessage.setStatus(WithdrawStatusEnum.VIA_AUDITING.name());
        } else {
            distributorCash.setDistributionCashStatus(WithdrawStatusEnum.FAIL_AUDITING.name());
            //还原可提现金额
            distributionService.addCashRebate(CurrencyUtil.sub(0, distributorCash.getPrice()), distributorCash.getDistributionId());
            memberWithdrawalMessage.setStatus(WithdrawStatusEnum.FAIL_AUDITING.name());
        }

        //修改分销提现申请
        boolean bool = this.updateById(distributorCash);
        if (bool) {
            //组织会员提现审核消息
            memberWithdrawalMessage.setMemberId(distribution.getMemberId());
            memberWithdrawalMessage.setPrice(distributorCash.getPrice());
            //发送消息
        }
        return distributorCash;
    }

}