package plus.qdt.modules.distribution.serviceimpl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.enums.SwitchEnum;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.distribution.entity.dos.Distribution;
import plus.qdt.modules.distribution.entity.dos.DistributionBind;
import plus.qdt.modules.distribution.entity.dto.DistributionDTO;
import plus.qdt.modules.distribution.entity.enums.DistributionStatusEnum;
import plus.qdt.modules.distribution.entity.vos.DistributionVO;
import plus.qdt.modules.distribution.mapper.DistributionBindMapper;
import plus.qdt.modules.distribution.service.DistributionBindService;
import plus.qdt.modules.distribution.service.DistributionService;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.DistributionSetting;
import plus.qdt.modules.system.entity.enums.DistributionSettingEnum;
import plus.qdt.modules.system.entity.enums.DistributionSustainEnum;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 分销绑定接口实现
 *
 * <AUTHOR>
 * @since 2020-03-24 23:04:56
 */
@Service
@RequiredArgsConstructor
public class DistributionBindServiceImpl extends ServiceImpl<DistributionBindMapper, DistributionBind> implements DistributionBindService {

    private final DistributionService distributionService;
    private final SettingClient settingClient;
    private final UserClient userClient;

    @Override
    public void bindingDistribution(String distributionId) {

        //判断用户是否登录，未登录不能进行绑定
        if (UserContext.getCurrentUser() == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        //如果已绑定则跳出
        if (getDistributionBind(UserContext.getCurrentUser().getId()) != null) {
            return;
        }
        //获取分销员（绑定关系说明：distributionId所属【分销员A】为当前会员的上级分销）
        //这里只记录绑定关系，不代表当前登录会员是否为分销员
        // 1、如果当前会员已是分销员，或者后续申请成为分销员，则绑定关系不变，默认【分销员A】为上级
        // 2、如果当前会员没有申请分销员，则绑定关系说明该会员是【分销员A】的客户
        // 3、绑定关系确认后，当前会员后续购买的所有分销商品，【分销员A】都可得到一级分销佣金，【分销员A】所绑定的上级得到二级分销佣金
        Distribution distribution = distributionService.getById(distributionId);
        // 不能绑定自己
        if (distribution != null && distribution.getMemberId() != UserContext.getCurrentId()) {
            Setting setting = settingClient.get(SettingEnum.DISTRIBUTION_SETTING.name());
            DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(), DistributionSetting.class);

            //判断关系绑定
            if (DistributionSettingEnum.NEW.name().equals(distributionSetting.getBinding())) {
                //判断是否是新用户，注册时间不是今天则跳出
                User user = userClient.getById(UserContext.getCurrentUser().getId());
                boolean isToday = DateUtil.isSameDay(user.getCreateTime(), DateUtil.date());
                if (!isToday) {
                    return;
                }
            }

            DistributionBind distributionBind = new DistributionBind();
            distributionBind.setMemberId(UserContext.getCurrentUser().getId());
            distributionBind.setNickName(UserContext.getCurrentUser().getNickName());
            distributionBind.setDistributionId(distributionId);
            distributionBind.setStatus(SwitchEnum.OPEN.name());
            distributionBind.setRebatePrice(0D);
            distributionBind.setOrderNum(0);
            distributionBind.setOrderPrice(0D);
            distributionBind.setLastLoginDate(new Date());
            distributionBind.setValidity(distributionSetting.getValidity().name());
            // 设置有效期
            if (DistributionSustainEnum.EXP.equals(distributionSetting.getValidity())) {
                distributionBind.setValidityDay(DateUtil.offsetDay(DateUtil.date(), distributionSetting.getValidityDay()));
            } else {
                //永久有效
                distributionBind.setValidityDay(DateUtil.parse("2099-12-31"));
            }
            this.save(distributionBind);

            //更新分销员推广人数
            distributionService.update(new LambdaUpdateWrapper<Distribution>()
                    .eq(Distribution::getId, distributionId).set(Distribution::getPeopleNum, distribution.getPeopleNum() + 1));
        }

    }

    @Override
    public DistributionBind getDistributionBind(String memberId) {
        return this.getOne(new LambdaQueryWrapper<DistributionBind>()
                .eq(DistributionBind::getMemberId, memberId)
                .eq(DistributionBind::getStatus, SwitchEnum.OPEN.name()), false);
    }

    @Override
    public List<DistributionVO> getUpDistribution(String memberId) {
        // 获取分销设置
        Setting setting = settingClient.get(SettingEnum.DISTRIBUTION_SETTING.name());
        DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(), DistributionSetting.class);
        // 结果集
        List<DistributionVO> distributions = new ArrayList<>();

        // 开启自我返现
        if (distributionSetting.getSelfCommission().equals(SwitchEnum.OPEN)) {
            Distribution distribution = distributionService.getByMemberId(memberId);
            if (distribution != null && distribution.getDistributionStatus()
                    .equals(DistributionStatusEnum.PASS.name())) {
                distributions.add(new DistributionVO(distribution));
            }
        }

        // 如果一级分销且开启自我返佣，则直接返回
        if (Boolean.TRUE.equals(satisfy(distributionSetting, distributions))) {
            return distributions;
        }

        // 获取关系
        DistributionBind distributionBind = this.getDistributionBind(memberId);
        // 如果没有绑定关系，则直接返回
        if (distributionBind == null) {
            return distributions;
        }
        Distribution distribution = distributionService.getById(distributionBind.getDistributionId());

        if (distribution != null && distribution.getDistributionStatus().equals(DistributionStatusEnum.PASS.name())) {
            distributions.add(new DistributionVO(distribution));
        } else {
            return distributions;
        }

        // 如果添加分销员后，关系数量符合层级数量则返回，否则再来一轮。
        if (Boolean.TRUE.equals(satisfy(distributionSetting, distributions))) {
            return distributions;
        }

        // 获取上级关系
        DistributionBind distributionBind2 = this.getDistributionBind(distribution.getMemberId());
        // 如果没有绑定关系，则直接返回
        if (distributionBind2 == null) {
            return distributions;
        }
        Distribution distribution2 = distributionService.getById(distributionBind2.getDistributionId());

        if (distribution2 != null && distribution2.getDistributionStatus().equals(DistributionStatusEnum.PASS.name())) {
            distributions.add(new DistributionVO(distribution2));
            return distributions;
        } else {
            return distributions;
        }
    }


    @Override
    public DistributionBind getUpDistributionBind(String distributionId) {
        // 获取当前分销员信息
        Distribution distribution = distributionService.getById(distributionId);
        // 获取绑定的上级分销员信息
        return getDistributionBind(distribution.getMemberId());
    }

    @Override
    public IPage<DistributionBind> distributionBindPage(PageVO pageVO, String distributionId) {
        return page(PageUtil.initPage(pageVO), new LambdaQueryWrapper<DistributionBind>()
                .eq(DistributionBind::getDistributionId, distributionId));
    }

    @Override
    public void addRebate(Double rebate, String memberId, Double distributionOrderPrice) {
        this.baseMapper.addRebate(rebate, memberId, distributionOrderPrice);
    }

    @Override
    public void statistics(DistributionDTO distributionDTO) {
        // 总邀请数量
        LambdaQueryWrapper<DistributionBind> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributionBind::getDistributionId, distributionDTO.getId());

        //今日新增邀请数

        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributionBind::getDistributionId, distributionDTO.getId());
        queryWrapper.ge(DistributionBind::getCreateTime, DateUtil.beginOfDay(DateUtil.date()));
        queryWrapper.le(DistributionBind::getCreateTime, DateUtil.endOfDay(DateUtil.date()));

        distributionDTO.setTodayCustomerNum(Convert.toInt(this.count(queryWrapper)) );
        Long count = this.count(new LambdaQueryWrapper<DistributionBind>().eq(DistributionBind::getDistributionId, distributionDTO.getId()));
        distributionDTO.setPeopleNum(count.intValue());
    }

    /**
     * 判断是否满足分销条件
     *
     * @param distributionSetting 分销配置
     * @param distributions       分销员列表
     * @return 是否满足
     */
    private Boolean satisfy(DistributionSetting distributionSetting, List<DistributionVO> distributions) {
        // 判定几级分销
        return distributions.size() >= Integer.parseInt(distributionSetting.getLevel());
    }
}
