package plus.qdt.modules.distribution.service;

import plus.qdt.modules.distribution.entity.dos.DistributionStore;
import plus.qdt.modules.distribution.entity.dto.DistributionStoreDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Author: <PERSON>ulbasaur
 * @CreateTime: 2023-10-07  14:08
 * @Description: 分销店铺设置
 * @Version: 1.0
 */
public interface DistributionStoreService extends IService<DistributionStore> {

    /**
     * 根据店铺ID获取分销设置
     *
     * @param storeId
     * @return 店铺分销设置
     */
    DistributionStore getByStoreId(String storeId);

    /**
     * 修改店铺分销设置
     *
     * @param storeId              店铺ID
     * @param storeName            店铺名称
     * @param distributionStoreDTO 店铺分销设置DTO
     * @return 修改结果
     */
    Boolean updateDistributionStore(String storeId, String storeName, DistributionStoreDTO distributionStoreDTO);


}