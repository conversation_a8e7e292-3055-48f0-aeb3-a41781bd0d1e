package plus.qdt.modules.distribution.mapper;

import plus.qdt.modules.distribution.entity.dos.DistributionGoods;
import plus.qdt.modules.distribution.entity.dos.DistributionStore;
import plus.qdt.modules.distribution.entity.vos.DistributionGoodsVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 分销商品数据处理层
 *
 * <AUTHOR>
 * @since 2020-03-24 23:04:56
 */
public interface DistributionGoodsMapper extends BaseMapper<DistributionGoods> {

    /**
     * 获取分销员未选择商品VO分页
     *
     * @param page           分页
     * @param queryWrapper   查询条件
     * @param distributionId 分销员ID
     * @return 分销员未选择商品VO分页
     */
    @Select("SELECT dg.* FROM li_distribution_goods dg WHERE dg.id NOT IN(SELECT distribution_goods_id FROM li_distribution_selected_goods WHERE distribution_id=${distributionId}) ${ew.customSqlSegment}")
    Page<DistributionGoodsVO> notSelectGoods(Page<DistributionGoodsVO> page, @Param(Constants.WRAPPER) Wrapper<DistributionGoodsVO> queryWrapper, String distributionId);

    /**
     * 获取分销员已选择分销商品VO分页
     *
     * @param page           分页
     * @param queryWrapper   查询条件
     * @param distributionId 分销员ID
     * @return 分销员已选择分销商品VO分页
     */
    @Select("SELECT dg.* FROM li_distribution_goods dg WHERE dg.id IN(SELECT distribution_goods_id FROM li_distribution_selected_goods WHERE distribution_id=${distributionId}) ${ew.customSqlSegment}")
    Page<DistributionGoodsVO> selectGoods(Page<DistributionGoodsVO> page, @Param(Constants.WRAPPER) Wrapper<DistributionGoodsVO> queryWrapper, String distributionId);

    /**
     * 获取分销商品VO分页
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 分销商品VO分页
     */
    @Select("SELECT dg.* FROM li_distribution_goods dg ${ew.customSqlSegment}")
    Page<DistributionGoods> getDistributionGoodsVO(Page<DistributionGoodsVO> page, @Param(Constants.WRAPPER) Wrapper<DistributionGoodsVO> queryWrapper);

    /**
     * 根据店铺ID获取商品ID列表
     *
     * @return 商品ID列表
     */
    @Select("SELECT goods_id FROM li_distribution_goods")
    List<String> getAllGoodsId();

    /**
     * 根据店铺ID获取商品ID列表
     *
     * @param storeId 店铺ID
     * @return 商品ID列表
     */
    @Select("SELECT goods_id FROM li_distribution_goods WHERE store_id = #{storeId}")
    List<String> getGoodsIdByStoreId(String storeId);

    /**
     * 通过商铺ID获取商铺分销设置
     * @param storeId
     * @return
     */
    @Select("SELECT * FROM li_distribution_store WHERE store_id = #{storeId}")
    DistributionStore getDistributionStoreByStoreId(String storeId);

}