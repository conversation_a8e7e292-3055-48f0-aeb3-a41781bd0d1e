package plus.qdt.controller.distribution;

import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.distribution.entity.dos.DistributionOrder;
import plus.qdt.modules.distribution.entity.dto.DistributionDTO;
import plus.qdt.modules.distribution.entity.vos.DistributionOrderSearchParams;
import plus.qdt.modules.distribution.service.DistributionOrderService;
import plus.qdt.modules.distribution.service.DistributionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 管理端,分销订单管理接口
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@RestController
@Tag(name = "管理端,分销订单管理接口")
@RequestMapping("/distribution/order")
@RequiredArgsConstructor
public class DistributionOrderController {

    private final DistributionOrderService distributionOrderService;
    private final DistributionService distributionService;

    @Operation(summary = "通过id获取分销订单")
    @GetMapping(value = "/get/{id}")
    public ResultMessage<DistributionOrder> get(@PathVariable String id) {

        return ResultUtil.data(distributionOrderService.getById(id));
    }

    @Operation(summary = "分页获取分销订单")
    @GetMapping(value = "/manager/page")
    public ResultMessage<IPage<DistributionOrder>> getByPage(DistributionOrderSearchParams distributionOrderSearchParams) {

        return ResultUtil.data(distributionOrderService.getDistributionOrderPage(distributionOrderSearchParams));
    }

    @Operation(summary = "获取分销订单列表")
    @GetMapping("/page")
    public ResultMessage<IPage<DistributionOrder>> distributionOrder(DistributionOrderSearchParams distributionOrderSearchParams) {
        //获取当前登录的分销员
        distributionOrderSearchParams.setDistributionId(distributionService.getDistribution().getId());
        //查询分销订单列表
        IPage<DistributionOrder> distributionOrderPage = distributionOrderService.getDistributionOrderPage(distributionOrderSearchParams);
        return ResultUtil.data(distributionOrderPage);
    }

    @Operation(summary = "获取店铺分销订单列表")
    @GetMapping("/store/page")
    public ResultMessage<IPage<DistributionOrder>> distributionStoreOrder(DistributionOrderSearchParams distributionOrderSearchParams) {
        String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
        //获取当前登录商家账号-查询当前店铺的分销订单
        distributionOrderSearchParams.setStoreId(storeId);
        //查询分销订单列表
        IPage<DistributionOrder> distributionOrderPage = distributionOrderService.getDistributionOrderPage(distributionOrderSearchParams);
        return ResultUtil.data(distributionOrderPage);
    }

    @Operation(summary = "获取当前会员的分销员信息")
    @GetMapping("/statistics")
    public ResultMessage<DistributionDTO> getStatisticsInfo() {
        //检查分销开关
        distributionService.checkDistributionSetting();

        return ResultUtil.data(distributionOrderService.statisticsDistributionInfo());
    }
}
