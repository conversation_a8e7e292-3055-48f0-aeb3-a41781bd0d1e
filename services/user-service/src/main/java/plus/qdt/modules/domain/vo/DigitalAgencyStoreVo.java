package plus.qdt.modules.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import plus.qdt.modules.store.entity.dos.Store;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@NoArgsConstructor
public class DigitalAgencyStoreVo {

    @Schema(title = "名称")
    private String storeName;

    @Schema(title = "logo")
    private String storeLogo;

    @Schema(title = "地址")
    private String address;

    @Schema(title = "会员")
    private Boolean vip;

    @Schema(title = "会员到期时间")
    private Date vipExpire;

    @Schema(title = "营业额")
    private BigDecimal turnover;

    public DigitalAgencyStoreVo(Store store) {
        this.storeName = store.getStoreName();
        this.storeLogo = store.getStoreLogo();
        this.address = store.getAddress();
    }
}
