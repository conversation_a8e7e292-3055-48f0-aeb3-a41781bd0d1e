package plus.qdt.modules.system.tpi.service;

import plus.qdt.modules.system.entity.dto.ThirdPartyAddressDTO;

import java.util.List;

/**
 * 三方地址服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface ThirdPartyAddressService {

    /**
     * 从第三方接口获取地址数据并保存到系统
     *
     * @return 同步结果
     */
    boolean syncAddressDataFromThirdParty();

    /**
     * 获取供应商类型
     *
     * @return 供应商类型
     */
    String getSupplierType();
}
