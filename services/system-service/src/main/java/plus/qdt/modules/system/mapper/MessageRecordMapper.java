package plus.qdt.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import plus.qdt.modules.system.entity.MessageRecord;

/**
 * 消息记录数据访问层
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface MessageRecordMapper extends BaseMapper<MessageRecord> {

    /**
     * 根据消息ID查询消息记录
     *
     * @param messageId 消息ID
     * @return 消息记录
     */
    @Select("SELECT * FROM message_record WHERE message_id = #{messageId} LIMIT 1")
    MessageRecord selectByMessageId(@Param("messageId") String messageId);

    /**
     * 根据业务类型和业务键查询消息记录
     *
     * @param bizType 业务类型
     * @param bizKey 业务键
     * @return 消息记录
     */
    @Select("SELECT * FROM message_record WHERE biz_type = #{bizType} AND biz_key = #{bizKey} LIMIT 1")
    MessageRecord selectByBizTypeAndKey(@Param("bizType") String bizType, @Param("bizKey") String bizKey);

    /**
     * 更新消息状态
     *
     * @param id 消息记录ID
     * @param status 新状态
     * @param errorMsg 错误信息
     * @return 影响行数
     */
    @Update("UPDATE message_record SET message_status = #{status}, last_error = #{errorMsg}, updated_at = NOW() WHERE id = #{id}")
    int updateMessageStatus(@Param("id") Long id, @Param("status") Integer status, @Param("errorMsg") String errorMsg);

    /**
     * 增加重试次数
     *
     * @param id 消息记录ID
     * @return 影响行数
     */
    @Update("UPDATE message_record SET retry_count = retry_count + 1, updated_at = NOW() WHERE id = #{id}")
    int incrementRetryCount(@Param("id") Long id);
}
