package plus.qdt.modules.sms.serviceimpl;

import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.sms.AliSmsUtil;
import plus.qdt.modules.sms.entity.dos.SmsTemplate;
import plus.qdt.modules.sms.mapper.SmsTemplateMapper;
import plus.qdt.modules.sms.service.SmsTemplateService;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信模板业务层实现
 * <AUTHOR>
 * @since 2021/1/30 4:27 下午
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsTemplateServiceImpl extends ServiceImpl<SmsTemplateMapper, SmsTemplate> implements SmsTemplateService {
    private final AliSmsUtil aliSmsUtil;


    @Override
    public void addSmsTemplate(SmsTemplate smsTemplate) {
        smsTemplate.setTemplateCode(aliSmsUtil.addSmsTemplate(smsTemplate));
        smsTemplate.setTemplateStatus(0);
        smsTemplate.setTemplateType(1);
        this.save(smsTemplate);
    }

    @Override
    public void deleteSmsTemplate(String id) {
        SmsTemplate smsTemplate = this.getById(id);
        if (smsTemplate.getTemplateCode() != null) {
            aliSmsUtil.deleteSmsTemplate(smsTemplate.getTemplateCode());
        }
        this.removeById(id);

    }

    @Override
    public void querySmsTemplate() {
            Map<String, Object> map = HashMap.newHashMap(16);
            //获取未审核通过的签名列表
            List<SmsTemplate> list = list(new LambdaQueryWrapper<SmsTemplate>().eq(SmsTemplate::getTemplateStatus, 0));
            //查询签名状态
            for (SmsTemplate smsTemplate : list) {
                map = aliSmsUtil.querySmsTemplate(smsTemplate.getTemplateCode());
                smsTemplate.setTemplateStatus((Integer) map.get("TemplateStatus"));
                smsTemplate.setReason(map.get("Reason").toString());
                smsTemplate.setTemplateCode(map.get("TemplateCode").toString());
                this.updateById(smsTemplate);
            }
    }

    @Override
    public void modifySmsTemplate(SmsTemplate smsTemplate) {
        aliSmsUtil.modifySmsTemplate(smsTemplate);
        smsTemplate.setTemplateStatus(0);
        this.updateById(smsTemplate);
    }

    @Override
    public Page<SmsTemplate> page(PageVO pageVO, Integer templateStatus) {
        return this.page(PageUtil.initPage(pageVO), new QueryWrapper<SmsTemplate>()
                .eq(templateStatus != null, "template_status", templateStatus));
    }
}
