package plus.qdt.controller.statistics;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.order.client.RefundOrderStatisticsClient;
import plus.qdt.modules.statistics.entity.dto.StatisticsQueryParam;
import plus.qdt.modules.statistics.entity.vo.RefundOrderStatisticsDataVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 退款统计接口
 *
 * <AUTHOR>
 * @since 2020/12/9 19:04
 */
@Tag(name = "退款统计接口")
@RestController
@RequestMapping("/statistics/refundOrder")
@RequiredArgsConstructor
public class RefundOrderStatisticsController {

    private final RefundOrderStatisticsClient refundOrderStatisticsClient;

    @Operation(summary = "获取退款统计列表")
    @GetMapping("/getByPage")
    public ResultMessage<Page<RefundOrderStatisticsDataVO>> getByPage(StatisticsQueryParam statisticsQueryParam) {
        return ResultUtil.data(refundOrderStatisticsClient.getRefundOrderStatisticsData(statisticsQueryParam));
    }

    @Operation(summary = "获取退款统计金额")
    @GetMapping("/getPrice")
    public ResultMessage<Object> getPrice(StatisticsQueryParam statisticsQueryParam) {
        return ResultUtil.data(refundOrderStatisticsClient.getRefundOrderStatisticsPrice(statisticsQueryParam));
    }
}
