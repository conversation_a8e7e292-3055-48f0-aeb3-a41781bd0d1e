package plus.qdt.modules.goods.serviceimpl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import plus.qdt.cache.Cache;
import plus.qdt.cache.CachePrefix;
import plus.qdt.common.tpi.yzh.config.YzhTpiProperties;
import plus.qdt.modules.goods.entity.dto.YZHGoodsResultDTO;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;
import plus.qdt.modules.goods.entity.dto.YZHRequestResultDTO;
import plus.qdt.common.utils.HttpUtils;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dos.YzhCategoryMapping;
import plus.qdt.modules.goods.entity.dto.GoodsDetailResult;
import plus.qdt.modules.goods.entity.dto.GoodsInitProgressDTO;
import plus.qdt.modules.goods.entity.dto.GoodsInitResult;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.goods.entity.vos.CategoryVO;
import plus.qdt.modules.goods.service.CategoryService;
import plus.qdt.modules.goods.service.GoodsService;
import plus.qdt.modules.goods.service.GoodsSkuService;
import plus.qdt.modules.goods.service.YzhCategoryMappingService;
import plus.qdt.modules.goods.service.YzhGoodsConvertService;
import plus.qdt.modules.goods.service.YzhGoodsService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 云中鹤商品服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class YzhGoodsServiceImpl implements YzhGoodsService {

    /**
     * 分页大小常量
     */
    private static final int PAGE_SIZE = 25; // 每页25条
    private static final int BATCH_SIZE = 25; // 批量处理25条

    private final YzhTpiProperties tpiProperties;
    private final Cache<Object> cache;
    private final YzhGoodsConvertService yzhGoodsConvertService;
    private final GoodsService goodsService;
    private final GoodsSkuService goodsSkuService;
    private final YzhCategoryMappingService yzhCategoryMappingService;
    private final CategoryService categoryService;

    @Override
    public boolean initGoodsData(List<YZHGoodsSkuDTO> goodsDataList, String userId, String scene, String extendId) {
        // 直接调用新的保存方法
        return saveGoodsData(goodsDataList, userId, scene, extendId);
    }

    @Override
    public boolean saveGoodsData(List<YZHGoodsSkuDTO> yzhGoodsList, String userId, String scene, String extendId) {
        try {
            log.info("开始保存云中鹤商品数据，共 {} 条，用户ID: {}, 场景: {}, 扩展ID: {}", 
                    yzhGoodsList.size(), userId, scene, extendId);
            
            if (yzhGoodsList == null || yzhGoodsList.isEmpty()) {
                log.warn("云中鹤商品数据为空，跳过保存");
                return true;
            }
            
            int successCount = 0;
            int failCount = 0;
            
            // 分批处理，每批最多25个商品
            int batchSize = BATCH_SIZE;
            for (int i = 0; i < yzhGoodsList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, yzhGoodsList.size());
                List<YZHGoodsSkuDTO> batchGoods = yzhGoodsList.subList(i, endIndex);
                
                log.info("处理第 {} 批商品数据，本批 {} 条", (i / batchSize + 1), batchGoods.size());
                
                try {
                    // 1. 数据验证
                    List<YZHGoodsSkuDTO> validGoods = batchGoods.stream()
                            .filter(this::validateGoodsData)
                            .collect(Collectors.toList());

                    if (validGoods.isEmpty()) {
                        log.warn("第 {} 批商品数据验证后为空，跳过处理", (i / batchSize + 1));
                        failCount += batchGoods.size();
                        continue;
                    }

                    // 2. 数据分类 - 区分新增和更新
                    GoodsDataClassification classification = classifyGoodsData(validGoods);

                    log.info("第 {} 批商品数据分类完成，新增: {} 条，更新: {} 条",
                            (i / batchSize + 1), classification.getNewGoods().size(), classification.getUpdateGoods().size());
                    
                    // 3. 分类映射处理 - 保存所有云中鹤分类数据
                    List<CategoryVO> systemCategories = categoryService.categoryTree();
                    processAndSaveAllCategoryMappings(validGoods, systemCategories);

                    // 4. 处理新增商品
                    int newGoodsProcessed = 0;
                    if (!classification.getNewGoods().isEmpty()) {
                        newGoodsProcessed = processNewGoods(classification.getNewGoods(), userId, scene, extendId, (i / batchSize + 1));
                    }

                    // 5. 处理更新商品
                    int updateGoodsProcessed = 0;
                    if (!classification.getUpdateGoods().isEmpty()) {
                        updateGoodsProcessed = processUpdateGoods(classification.getUpdateGoods(), userId, scene, extendId, (i / batchSize + 1));
                    }

                    successCount += newGoodsProcessed + updateGoodsProcessed;
                    
                } catch (Exception e) {
                    log.error("第 {} 批商品数据处理失败: {}", (i / batchSize + 1), e.getMessage(), e);
                    failCount += batchGoods.size();
                }
            }
            
            log.info("云中鹤商品数据处理完成，总数: {}, 成功: {}, 失败: {} (包含新增和更新)",
                    yzhGoodsList.size(), successCount, failCount);
            
            return successCount > 0;
            
        } catch (Exception e) {
            log.error("保存云中鹤商品数据时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean processGoodsPage(YZHGoodsResultDTO goodsResult, String userId, String scene, String extendId) {
        try {
            if (goodsResult == null || goodsResult.getGoodsSkuList() == null) {
                log.warn("商品数据为空，跳过处理");
                return true;
            }
            
            log.info("处理第 {} 页商品数据，共 {} 条，用户ID: {}, 场景: {}, 扩展ID: {}", 
                    goodsResult.getPageNum(), goodsResult.getGoodsSkuList().size(), userId, scene, extendId);
            
            return initGoodsData(goodsResult.getGoodsSkuList(), userId, scene, extendId);
            
        } catch (Exception e) {
            log.error("处理商品分页数据时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证商品数据
     *
     * @param goodsSku 商品SKU数据
     * @return 验证结果
     */
    private boolean validateGoodsData(YZHGoodsSkuDTO goodsSku) {
        if (goodsSku == null) {
            log.warn("商品数据为空");
            return false;
        }
        
        if (goodsSku.getGoodsSkuCode() == null || goodsSku.getGoodsSkuCode().trim().isEmpty()) {
            log.warn("商品SKU编码为空");
            return false;
        }
        
        if (goodsSku.getGoodsSkuName() == null || goodsSku.getGoodsSkuName().trim().isEmpty()) {
            log.warn("商品SKU名称为空");
            return false;
        }
        
        return true;
    }

    @Override
    public GoodsInitResult executeGoodsInitialization(String userId, String scene, String extendId) {
        try {
            log.info("开始执行云中鹤商品数据初始化，用户ID: {}, 场景: {}, 扩展ID: {}...", userId, scene, extendId);

            // 1. 获取AccessToken
            String accessToken = getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                throw new RuntimeException("获取AccessToken失败，无法进行商品初始化");
            }

            // 2. 分页获取商品数据并保存
            return fetchAndSaveAllGoodsPages(accessToken, userId, scene, extendId);

        } catch (Exception e) {
            log.error("执行云中鹤商品数据初始化时发生异常: {}", e.getMessage(), e);
            return GoodsInitResult.empty();
        }
    }

    @Override
    public GoodsInitResult executeGoodsInitializationWithProgress(String taskId, String userId, String scene, String extendId) {
        String progressKey = CachePrefix.GOODS_INIT_PROGRESS.getPrefix() + taskId;
        GoodsInitProgressDTO progress = null;

        try {
            log.info("开始执行云中鹤商品数据初始化（带进度），任务ID: {}, 用户ID: {}, 场景: {}, 扩展ID: {}...", taskId, userId, scene, extendId);

            // 初始化进度信息
            progress = initializeProgress(taskId, progressKey);

            // 1. 获取AccessToken
            updateProgress(progress, progressKey, 5, "正在获取访问令牌...", null);
            String accessToken = getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                throw new RuntimeException("获取AccessToken失败，无法进行商品初始化");
            }

            // 2. 分页获取商品数据并保存（带进度更新）
            updateProgress(progress, progressKey, 10, "开始获取商品数据...", null);
            return fetchAndSaveAllGoodsPagesWithProgress(accessToken, userId, scene, extendId, progress, progressKey);

        } catch (Exception e) {
            log.error("执行云中鹤商品数据初始化时发生异常: {}", e.getMessage(), e);

            if (progress != null) {
                updateProgress(progress, progressKey, 0, "商品初始化异常", GoodsInitProgressDTO.Status.FAILED.name());
                progress.setEndTime(System.currentTimeMillis());
                progress.setErrorMessage("执行异常: " + e.getMessage());
                cache.put(progressKey, progress, 3600L);
            }

            return GoodsInitResult.empty();
        }
    }

    @Override
    public GoodsDetailResult executeGoodsDetail(List<String> goodsSkuCodeList, String userId, String scene, String extendId) {
        try {
            log.info("开始获取云中鹤商品详情，SKU数量: {}, 用户ID: {}, 场景: {}, 扩展ID: {}",
                    goodsSkuCodeList.size(), userId, scene, extendId);

            if (goodsSkuCodeList == null || goodsSkuCodeList.isEmpty()) {
                log.warn("SKU编码列表为空，跳过商品详情获取");
                return GoodsDetailResult.empty();
            }

            // 获取AccessToken
            String accessToken = getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                throw new RuntimeException("获取AccessToken失败，无法获取商品详情");
            }

            List<YZHGoodsSkuDTO> goodsDetailList = new ArrayList<>();

            // 分批处理，每批最多25个SKU
            int batchSize = BATCH_SIZE;
            for (int i = 0; i < goodsSkuCodeList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, goodsSkuCodeList.size());
                List<String> batchSkuCodes = goodsSkuCodeList.subList(i, endIndex);

                log.info("处理第 {} 批SKU详情，本批 {} 个", (i / batchSize + 1), batchSkuCodes.size());

                // 获取当前批次的商品详情
                List<YZHGoodsSkuDTO> batchDetails = fetchGoodsDetailBatch(accessToken, batchSkuCodes);
                if (batchDetails != null && !batchDetails.isEmpty()) {
                    goodsDetailList.addAll(batchDetails);
                }
            }

            log.info("商品详情获取完成，总数: {}，开始保存商品详情数据", goodsDetailList.size());

            // 保存商品详情数据，传递用户上下文信息
            if (!goodsDetailList.isEmpty()) {
                boolean saveResult = initGoodsData(goodsDetailList, userId, scene, extendId);
                if (saveResult) {
                    log.info("商品详情数据保存成功");
                } else {
                    log.error("商品详情数据保存失败");
                }
            }

            return GoodsDetailResult.success(goodsDetailList, goodsDetailList.size());

        } catch (Exception e) {
            log.error("获取云中鹤商品详情时发生异常: {}", e.getMessage(), e);
            return GoodsDetailResult.empty();
        }
    }

    /**
     * 获取AccessToken
     *
     * @return AccessToken
     */
    private String getAccessToken() {
        try {
            // 先从缓存中获取
            String cacheKey = CachePrefix.TPI_YZH_TOKEN.getPrefix();
            Object cachedTokenObj = cache.get(cacheKey);
            if (cachedTokenObj != null) {
                String cachedToken = cachedTokenObj.toString();
                if (!cachedToken.isEmpty()) {
                    log.debug("从缓存中获取到AccessToken");
                    return cachedToken;
                }
            }

            // 缓存中没有，调用接口获取
            log.info("缓存中无AccessToken，开始调用接口获取...");

            Map<String, Object> params = new HashMap<>();
            params.put("appKey", tpiProperties.getAppKey());
            params.put("appSecret", tpiProperties.getAppSecret());
            params.put("grantType", tpiProperties.getGrantType());

            String response = HttpUtils.doPostWithJson(tpiProperties.getTokenUrl(), params);

            if (response == null || response.isEmpty()) {
                log.error("获取AccessToken失败，响应为空");
                return null;
            }

            YZHRequestResultDTO tokenResponse;
            try {
                log.debug("AccessToken响应内容: {}", response);
                tokenResponse = JSONObject.parseObject(response, YZHRequestResultDTO.class);
            } catch (Exception e) {
                log.error("AccessToken JSON解析失败，响应内容: {}, 异常: {}", response, e.getMessage());
                return null;
            }

            if (tokenResponse == null || !Boolean.TRUE.equals(tokenResponse.getSuccess())) {
                log.error("获取AccessToken失败: {}", tokenResponse != null ? tokenResponse.getDesc() : "解析失败");
                return null;
            }

            String accessToken = (String) tokenResponse.getResult();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("获取到的AccessToken为空");
                return null;
            }

            // 将token存入缓存，有效期设为1小时
            cache.put(cacheKey, accessToken, 3600L);
            log.info("AccessToken获取成功并已缓存");

            return accessToken;

        } catch (Exception e) {
            log.error("获取AccessToken时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 分页获取并保存所有商品数据
     */
    private GoodsInitResult fetchAndSaveAllGoodsPages(String accessToken, String userId, String scene, String extendId) {
        try {
            List<YZHGoodsSkuDTO> allGoodsList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = PAGE_SIZE; // 每页25条
            int totalProcessed = 0;

            while (true) {
                log.info("开始获取第 {} 页商品数据，每页 {} 条", pageNum, pageSize);

                // 获取单页数据
                YZHGoodsResultDTO result = fetchSinglePageGoods(accessToken, pageNum, pageSize);

                if (result == null || result.getGoodsSkuList() == null || result.getGoodsSkuList().isEmpty()) {
                    log.info("第 {} 页商品数据为空，结束分页获取", pageNum);
                    break;
                }

                log.info("第 {} 页获取到 {} 条商品数据", pageNum, result.getGoodsSkuList().size());

                // 获取当前页商品的详情信息
                List<String> skuCodes = result.getGoodsSkuList().stream()
                        .map(YZHGoodsSkuDTO::getGoodsSkuCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                List<YZHGoodsSkuDTO> detailedGoodsList = fetchGoodsDetailsBySkuCodes(accessToken, skuCodes);

                // 保存商品详情数据（包含完整信息）
                boolean saveResult = saveGoodsData(detailedGoodsList, userId, scene, extendId);
                if (!saveResult) {
                    log.warn("第 {} 页商品数据保存失败，继续处理下一页", pageNum);
                }

                // 收集所有商品数据
                allGoodsList.addAll(detailedGoodsList);
                totalProcessed += detailedGoodsList.size();

                // 检查是否还有更多页
                if (result.getGoodsSkuList().size() < pageSize) {
                    log.info("当前页数据量小于页大小，已获取完所有数据");
                    break;
                }

                pageNum++;
            }

            log.info("商品数据分页获取完成，总页数: {}, 总商品数: {}", pageNum - 1, totalProcessed);
            return GoodsInitResult.success(totalProcessed, allGoodsList);

        } catch (Exception e) {
            log.error("分页获取商品数据时发生异常: {}", e.getMessage(), e);
            return GoodsInitResult.empty();
        }
    }

    /**
     * 获取单页商品数据
     */
    private YZHGoodsResultDTO fetchSinglePageGoods(String accessToken, int pageNum, int pageSize) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("accessToken", accessToken);
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);

            // 发送HTTP请求
            String goodsListUrl = tpiProperties.getGoodsListUrl();
            String response = HttpUtils.doPostWithJson(goodsListUrl, params);

            if (response == null || response.isEmpty()) {
                log.error("第 {} 页商品数据获取失败，响应为空", pageNum);
                return null;
            }

            // 解析响应数据
            YZHRequestResultDTO goodsResponse;
            try {
                log.debug("第 {} 页商品数据响应内容: {}", pageNum, response);
                goodsResponse = JSONObject.parseObject(response, YZHRequestResultDTO.class);
            } catch (Exception e) {
                log.error("第 {} 页商品数据JSON解析失败，响应内容: {}, 异常: {}", pageNum, response, e.getMessage());
                return null;
            }

            if (goodsResponse == null || !Boolean.TRUE.equals(goodsResponse.getSuccess())) {
                log.error("第 {} 页商品数据解析失败或接口返回失败: {}", pageNum,
                        goodsResponse != null ? goodsResponse.getDesc() : "解析失败");
                return null;
            }

            // 注意：这里需要处理result字段，可能是String类型需要再次解析
            Object resultObj = goodsResponse.getResult();
            YZHGoodsResultDTO result;

            if (resultObj instanceof String) {
                result = JSONObject.parseObject((String) resultObj, YZHGoodsResultDTO.class);
            } else {
                result = JSONObject.parseObject(JSONObject.toJSONString(resultObj), YZHGoodsResultDTO.class);
            }

            if (result == null || result.getGoodsSkuList() == null || result.getGoodsSkuList().isEmpty()) {
                return null;
            }

            return result;

        } catch (Exception e) {
            log.error("获取第 {} 页商品数据时发生异常: {}", pageNum, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据SKU编码列表获取商品详情
     */
    private List<YZHGoodsSkuDTO> fetchGoodsDetailsBySkuCodes(String accessToken, List<String> skuCodes) {
        try {
            if (skuCodes == null || skuCodes.isEmpty()) {
                return new ArrayList<>();
            }

            List<YZHGoodsSkuDTO> allDetails = new ArrayList<>();

            // 分批处理，每批最多25个SKU
            int batchSize = BATCH_SIZE;
            for (int i = 0; i < skuCodes.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, skuCodes.size());
                List<String> batchSkuCodes = skuCodes.subList(i, endIndex);

                log.info("获取第 {} 批商品详情，本批 {} 个SKU", (i / batchSize + 1), batchSkuCodes.size());

                // 构建请求参数
                Map<String, Object> params = new HashMap<>();
                params.put("accessToken", accessToken);
                params.put("goodsSkuCode", batchSkuCodes); // 注意：参数名是 goodsSkuCode，不是 goodsSkuCodeList

                // 发送HTTP请求
                String goodsDetailUrl = tpiProperties.getGoodsDetailUrl();
                String response = HttpUtils.doPostWithJson(goodsDetailUrl, params);

                if (response == null || response.isEmpty()) {
                    log.error("第 {} 批商品详情获取失败，响应为空", (i / batchSize + 1));
                    continue;
                }

                // 解析响应数据
                YZHRequestResultDTO detailResponse;
                try {
                    log.debug("第 {} 批商品详情响应内容: {}", (i / batchSize + 1), response);
                    detailResponse = JSONObject.parseObject(response, YZHRequestResultDTO.class);
                } catch (Exception e) {
                    log.error("第 {} 批商品详情JSON解析失败，响应内容: {}, 异常: {}", (i / batchSize + 1), response, e.getMessage());
                    continue;
                }

                if (detailResponse == null || !Boolean.TRUE.equals(detailResponse.getSuccess())) {
                    log.error("第 {} 批商品详情解析失败或接口返回失败: {}", (i / batchSize + 1),
                            detailResponse != null ? detailResponse.getDesc() : "解析失败");
                    continue;
                }

                // 处理result字段
                Object resultObj = detailResponse.getResult();
                if (resultObj == null) {
                    log.warn("第 {} 批商品详情结果为空", (i / batchSize + 1));
                    continue;
                }

                List<YZHGoodsSkuDTO> batchDetails;
                if (resultObj instanceof String) {
                    batchDetails = JSONArray.parseArray((String) resultObj, YZHGoodsSkuDTO.class);
                } else {
                    batchDetails = JSONArray.parseArray(JSONObject.toJSONString(resultObj), YZHGoodsSkuDTO.class);
                }

                if (batchDetails != null && !batchDetails.isEmpty()) {
                    allDetails.addAll(batchDetails);
                    log.info("第 {} 批商品详情获取成功，返回 {} 条详情", (i / batchSize + 1), batchDetails.size());
                }
            }

            log.info("商品详情获取完成，总共获取 {} 条详情", allDetails.size());
            return allDetails;

        } catch (Exception e) {
            log.error("获取商品详情时发生异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量获取商品详情
     */
    private List<YZHGoodsSkuDTO> fetchGoodsDetailBatch(String accessToken, List<String> skuCodes) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("accessToken", accessToken);
            params.put("goodsSkuCode", skuCodes);

            // 发送HTTP请求
            String goodsDetailUrl = tpiProperties.getGoodsDetailUrl();
            String response = HttpUtils.doPostWithJson(goodsDetailUrl, params);

            if (response == null || response.isEmpty()) {
                log.error("商品详情获取失败，响应为空，SKU数量: {}", skuCodes.size());
                return new ArrayList<>();
            }

            // 解析响应数据
            YZHRequestResultDTO detailResponse;
            try {
                log.debug("商品详情响应内容，SKU数量: {}, 响应: {}", skuCodes.size(), response);
                detailResponse = JSONObject.parseObject(response, YZHRequestResultDTO.class);
            } catch (Exception e) {
                log.error("商品详情JSON解析失败，SKU数量: {}, 响应内容: {}, 异常: {}", skuCodes.size(), response, e.getMessage());
                return new ArrayList<>();
            }

            if (detailResponse == null || !Boolean.TRUE.equals(detailResponse.getSuccess())) {
                log.error("商品详情解析失败或接口返回失败: {}, SKU数量: {}",
                        detailResponse != null ? detailResponse.getDesc() : "解析失败", skuCodes.size());
                return new ArrayList<>();
            }

            // 处理result字段
            Object resultObj = detailResponse.getResult();
            if (resultObj == null) {
                log.warn("商品详情结果为空，SKU数量: {}", skuCodes.size());
                return new ArrayList<>();
            }

            List<YZHGoodsSkuDTO> detailList;
            if (resultObj instanceof String) {
                detailList = JSONArray.parseArray((String) resultObj, YZHGoodsSkuDTO.class);
            } else {
                detailList = JSONArray.parseArray(JSONObject.toJSONString(resultObj), YZHGoodsSkuDTO.class);
            }

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            log.info("批量获取商品详情成功，请求SKU数量: {}, 返回详情数量: {}", skuCodes.size(), detailList.size());
            return detailList;

        } catch (Exception e) {
            log.error("批量获取商品详情时发生异常，SKU数量: {}, 异常: {}", skuCodes.size(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 初始化进度信息
     */
    private GoodsInitProgressDTO initializeProgress(String taskId, String progressKey) {
        GoodsInitProgressDTO progress = new GoodsInitProgressDTO();
        progress.setTaskId(taskId);
        progress.setProgress(0);
        progress.setStatus(GoodsInitProgressDTO.Status.RUNNING.name());
        progress.setCurrentStep("正在启动云中鹤商品初始化...");
        progress.setStartTime(System.currentTimeMillis());
        progress.setProcessedCount(0);
        progress.setTotalCount(0);

        // 将进度信息存储到缓存中
        cache.put(progressKey, progress, 3600L); // 缓存1小时

        log.info("初始化进度信息完成，任务ID: {}", taskId);
        return progress;
    }

    /**
     * 更新进度信息（带进度回退保护）
     */
    private void updateProgress(GoodsInitProgressDTO progress, String progressKey,
                               Integer progressValue, String currentStep, String status) {
        // 从缓存中获取当前进度，防止进度回退
        GoodsInitProgressDTO cachedProgress = (GoodsInitProgressDTO) cache.get(progressKey);
        if (cachedProgress != null && cachedProgress.getProgress() != null) {
            // 如果新的进度值小于缓存中的进度值，则不更新进度，只更新步骤信息
            if (progressValue < cachedProgress.getProgress()) {
                log.warn("检测到进度回退，跳过更新：当前缓存进度={}%, 新进度={}%, 步骤: {}",
                        cachedProgress.getProgress(), progressValue, currentStep);

                // 只更新步骤信息，保持原有进度
                progress.setProgress(cachedProgress.getProgress());
                progress.setCurrentStep(currentStep);
                if (status != null) {
                    progress.setStatus(status);
                }

                // 更新缓存
                cache.put(progressKey, progress, 3600L);

                log.debug("保持进度: {}% - {}", cachedProgress.getProgress(), currentStep);
                return;
            }
        }

        // 正常更新进度
        progress.setProgress(progressValue);
        progress.setCurrentStep(currentStep);
        if (status != null) {
            progress.setStatus(status);
        }

        // 更新缓存
        cache.put(progressKey, progress, 3600L);

        log.debug("更新进度: {}% - {}", progressValue, currentStep);
    }

    /**
     * 分页获取并保存所有商品数据（带进度更新）
     * 重新设计：先获取所有数据，再分阶段处理
     */
    private GoodsInitResult fetchAndSaveAllGoodsPagesWithProgress(String accessToken, String userId, String scene, String extendId,
                                                                 GoodsInitProgressDTO progress, String progressKey) {
        try {
            // 阶段1：获取所有商品数据 (10%-40%)
            updateProgress(progress, progressKey, 10, "开始获取所有商品数据...", null);
            List<YZHGoodsSkuDTO> allGoodsList = fetchAllGoodsData(accessToken, progress, progressKey);

            if (allGoodsList.isEmpty()) {
                log.info("未获取到任何商品数据，结束处理");
                updateProgress(progress, progressKey, 100, "商品初始化完成（无数据）", GoodsInitProgressDTO.Status.COMPLETED.name());
                return GoodsInitResult.success(0, new ArrayList<>());
            }

            int totalCount = allGoodsList.size();
            progress.setTotalCount(totalCount);
            log.info("成功获取所有商品数据，总数: {}", totalCount);

            // 阶段2：处理分类数据映射关系 (40%-55%)
            updateProgress(progress, progressKey, 40, "开始处理分类数据映射关系...", null);
            processAllCategoryMappingsWithProgress(allGoodsList, progress, progressKey, 40, 15);

            // 阶段3：数据分类和验证 (55%-60%)
            updateProgress(progress, progressKey, 55, "开始数据分类和验证...", null);
            GoodsDataClassification classification = classifyGoodsData(allGoodsList);
            log.info("数据分类完成，新增: {}, 更新: {}",
                    classification.getNewGoods().size(), classification.getUpdateGoods().size());

            // 阶段4：保存商品数据 (60%-80%)
            updateProgress(progress, progressKey, 60, "开始保存商品数据...", null);
            int newGoodsProcessed = processNewGoodsWithProgress(classification.getNewGoods(),
                    userId, scene, extendId, progress, progressKey, 60, 20);

            // 阶段5：更新商品数据 (80%-95%)
            updateProgress(progress, progressKey, 80, "开始更新商品数据...", null);
            int updateGoodsProcessed = processUpdateGoodsWithProgress(classification.getUpdateGoods(),
                    userId, scene, extendId, progress, progressKey, 80, 15);

            int totalProcessed = newGoodsProcessed + updateGoodsProcessed;
            progress.setProcessedCount(totalProcessed);

            // 完成处理
            updateProgress(progress, progressKey, 100, "商品初始化完成", GoodsInitProgressDTO.Status.COMPLETED.name());
            progress.setEndTime(System.currentTimeMillis());
            progress.setResultMessage(String.format("成功处理 %d 条商品数据", totalProcessed));
            cache.put(progressKey, progress, 3600L);

            log.info("商品数据处理完成，总数: {}, 成功: {}", totalCount, totalProcessed);
            return GoodsInitResult.success(totalProcessed, allGoodsList);
        } catch (Exception e) {
            log.error("分页获取商品数据时发生异常: {}", e.getMessage(), e);

            updateProgress(progress, progressKey, 0, "商品数据获取异常", GoodsInitProgressDTO.Status.FAILED.name());
            progress.setEndTime(System.currentTimeMillis());
            progress.setErrorMessage("数据获取异常: " + e.getMessage());
            cache.put(progressKey, progress, 3600L);

            return GoodsInitResult.empty();
        }
    }

    /**
     * 处理并保存所有分类映射数据
     * 保存云中鹤的所有分类数据，如果一二三级分类都匹配不上则把系统一二三级分类数据置空
     */
    private void processAndSaveAllCategoryMappings(List<YZHGoodsSkuDTO> validGoods, List<CategoryVO> systemCategories) {
        try {
            log.info("开始处理分类映射，商品数量: {}, 系统分类数量: {}", validGoods.size(), systemCategories.size());

            // 收集所有唯一的云中鹤分类组合
            Set<String> uniqueCategoryKeys = new HashSet<>();
            Map<String, YZHGoodsSkuDTO> categoryToGoodsMap = new HashMap<>();

            for (YZHGoodsSkuDTO goods : validGoods) {
                String categoryKey = buildCategoryKey(
                    goods.getFirstCategoryCode(),
                    goods.getSecondCategoryCode(),
                    goods.getLastCategoryCode()
                );

                if (!uniqueCategoryKeys.contains(categoryKey)) {
                    uniqueCategoryKeys.add(categoryKey);
                    categoryToGoodsMap.put(categoryKey, goods);
                }
            }

            log.info("发现 {} 个唯一的云中鹤分类组合", uniqueCategoryKeys.size());

            // 为每个唯一的分类组合创建或更新映射
            for (String categoryKey : uniqueCategoryKeys) {
                YZHGoodsSkuDTO goods = categoryToGoodsMap.get(categoryKey);
                processAndSaveSingleCategoryMapping(goods, systemCategories);
            }

            log.info("分类映射处理完成");

        } catch (Exception e) {
            log.error("处理分类映射时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理并保存单个分类映射
     */
    private void processAndSaveSingleCategoryMapping(YZHGoodsSkuDTO goods, List<CategoryVO> systemCategories) {
        try {
            // 检查是否已存在映射
            YzhCategoryMapping existingMapping = yzhCategoryMappingService.findMappingByYzhCategory(
                goods.getFirstCategoryCode(),
                goods.getSecondCategoryCode(),
                goods.getLastCategoryCode()
            );

            YzhCategoryMapping mapping;
            boolean isUpdate = false;

            if (existingMapping != null) {
                // 存在则更新
                mapping = existingMapping;
                isUpdate = true;
                log.debug("分类映射已存在，将进行更新: {}-{}-{}",
                    goods.getFirstCategoryCode(), goods.getSecondCategoryCode(), goods.getLastCategoryCode());
            } else {
                // 不存在则新增
                mapping = new YzhCategoryMapping();
                log.debug("分类映射不存在，将进行新增: {}-{}-{}",
                    goods.getFirstCategoryCode(), goods.getSecondCategoryCode(), goods.getLastCategoryCode());
            }

            // 设置云中鹤分类信息（必须保存）
            mapping.setYzhFirstCategoryCode(goods.getFirstCategoryCode());
            mapping.setYzhFirstCategoryName(goods.getFirstCategoryName());
            mapping.setYzhSecondCategoryCode(goods.getSecondCategoryCode());
            mapping.setYzhSecondCategoryName(goods.getSecondCategoryName());
            mapping.setYzhLastCategoryCode(goods.getLastCategoryCode());
            mapping.setYzhLastCategoryName(goods.getLastCategoryName());

            // 设置平台信息
            mapping.setPlatformCode(SupplierEnum.YZH.getDefaultId());
            mapping.setPlatformName(SupplierEnum.YZH.getDescription());

            // 尝试自动匹配系统分类
            plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.CategoryMatchResult matchResult = attemptCategoryMatching(goods, systemCategories);

            if (matchResult != null && matchResult.getHasMatch() && matchResult.getBestMatch() != null) {
                // 匹配成功，设置系统分类信息
                plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.SystemCategoryMatch bestMatch = matchResult.getBestMatch();

                // 根据匹配层级设置分类信息
                if (bestMatch.getLevel() >= 0) {
                    mapping.setSystemFirstCategoryId(bestMatch.getCategoryId());
                    mapping.setSystemFirstCategoryName(bestMatch.getCategoryName());
                }
                if (bestMatch.getLevel() >= 1) {
                    mapping.setSystemSecondCategoryId(bestMatch.getCategoryId());
                    mapping.setSystemSecondCategoryName(bestMatch.getCategoryName());
                }
                if (bestMatch.getLevel() >= 2) {
                    mapping.setSystemLastCategoryId(bestMatch.getCategoryId());
                    mapping.setSystemLastCategoryName(bestMatch.getCategoryName());
                }

                mapping.setMatchStatus(YzhCategoryMapping.MatchStatus.MATCHED.getCode());
                mapping.setMatchLevel(bestMatch.getLevel());
                mapping.setSimilarityScore(bestMatch.getSimilarityScore());
                mapping.setMappingType(YzhCategoryMapping.MappingType.AUTO.getCode());

                log.info("分类自动匹配成功: {}, 匹配级别: {}, 相似度: {}",
                    buildCategoryDisplayName(goods), bestMatch.getLevel(), bestMatch.getSimilarityScore());
            } else {
                // 匹配失败，系统分类字段置空
                mapping.setSystemFirstCategoryId(null);
                mapping.setSystemFirstCategoryName(null);
                mapping.setSystemSecondCategoryId(null);
                mapping.setSystemSecondCategoryName(null);
                mapping.setSystemLastCategoryId(null);
                mapping.setSystemLastCategoryName(null);

                mapping.setMatchStatus(YzhCategoryMapping.MatchStatus.FAILED.getCode());
                mapping.setMatchLevel(0);
                mapping.setSimilarityScore(BigDecimal.ZERO);
                mapping.setMappingType(YzhCategoryMapping.MappingType.AUTO.getCode());

                log.warn("分类匹配失败，系统分类置空: {}", buildCategoryDisplayName(goods));
            }

            // 设置其他字段
            mapping.setGoodsSkuCode(goods.getGoodsSkuCode());
            if (!isUpdate) {
                mapping.setGoodsCount(1);
            }
            mapping.setAuditStatus(YzhCategoryMapping.AuditStatus.PENDING.getCode());

            // 保存或更新映射
            if (isUpdate) {
                yzhCategoryMappingService.updateById(mapping);
                log.debug("分类映射更新成功: {}", buildCategoryDisplayName(goods));
            } else {
                yzhCategoryMappingService.save(mapping);
                log.debug("分类映射保存成功: {}", buildCategoryDisplayName(goods));
            }

        } catch (Exception e) {
            log.error("处理单个分类映射时发生异常，商品: {}, 异常: {}",
                buildCategoryDisplayName(goods), e.getMessage(), e);
        }
    }

    /**
     * 尝试分类匹配 - 使用 YzhCategoryMappingService 的匹配逻辑
     */
    private plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.CategoryMatchResult attemptCategoryMatching(YZHGoodsSkuDTO goods, List<CategoryVO> systemCategories) {
        try {
            // 构建云中鹤分类信息
            plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.YzhCategoryInfo yzhCategory =
                new plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.YzhCategoryInfo();
            yzhCategory.setFirstCategoryCode(goods.getFirstCategoryCode());
            yzhCategory.setFirstCategoryName(goods.getFirstCategoryName());
            yzhCategory.setSecondCategoryCode(goods.getSecondCategoryCode());
            yzhCategory.setSecondCategoryName(goods.getSecondCategoryName());
            yzhCategory.setLastCategoryCode(goods.getLastCategoryCode());
            yzhCategory.setLastCategoryName(goods.getLastCategoryName());

            // 使用 YzhCategoryMappingService 进行匹配
            return yzhCategoryMappingService.autoMatchCategory(yzhCategory, systemCategories);

        } catch (Exception e) {
            log.error("分类匹配时发生异常: {}", e.getMessage(), e);
            // 返回一个失败的匹配结果
            plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.CategoryMatchResult failedResult =
                new plus.qdt.modules.goods.entity.dto.YzhCategoryMappingDTO.CategoryMatchResult();
            failedResult.setHasMatch(false);
            return failedResult;
        }
    }

    /**
     * 构建分类键
     */
    private String buildCategoryKey(String firstCode, String secondCode, String lastCode) {
        return String.format("%s|%s|%s",
            firstCode != null ? firstCode : "",
            secondCode != null ? secondCode : "",
            lastCode != null ? lastCode : "");
    }

    /**
     * 构建云中鹤分类显示名称
     */
    private String buildCategoryDisplayName(YZHGoodsSkuDTO goods) {
        return String.format("%s > %s > %s",
            goods.getFirstCategoryName() != null ? goods.getFirstCategoryName() : "未知",
            goods.getSecondCategoryName() != null ? goods.getSecondCategoryName() : "未知",
            goods.getLastCategoryName() != null ? goods.getLastCategoryName() : "未知");
    }





    // ==================== 数据分类和处理方法 ====================

    /**
     * 商品数据分类结果
     */
    private static class GoodsDataClassification {
        private List<YZHGoodsSkuDTO> newGoods = new ArrayList<>();
        private List<YZHGoodsSkuDTO> updateGoods = new ArrayList<>();
        private Map<String, GoodsSku> existingSkuMap = new HashMap<>();

        public List<YZHGoodsSkuDTO> getNewGoods() { return newGoods; }
        public void setNewGoods(List<YZHGoodsSkuDTO> newGoods) { this.newGoods = newGoods; }

        public List<YZHGoodsSkuDTO> getUpdateGoods() { return updateGoods; }
        public void setUpdateGoods(List<YZHGoodsSkuDTO> updateGoods) { this.updateGoods = updateGoods; }

        public Map<String, GoodsSku> getExistingSkuMap() { return existingSkuMap; }
        public void setExistingSkuMap(Map<String, GoodsSku> existingSkuMap) { this.existingSkuMap = existingSkuMap; }
    }

    /**
     * 对商品数据进行分类：新增 vs 更新
     *
     * @param yzhGoodsList 云中鹤商品列表
     * @return 分类结果
     */
    private GoodsDataClassification classifyGoodsData(List<YZHGoodsSkuDTO> yzhGoodsList) {
        GoodsDataClassification classification = new GoodsDataClassification();

        try {
            if (yzhGoodsList == null || yzhGoodsList.isEmpty()) {
                return classification;
            }

            // 提取所有SKU编码
            List<String> skuCodes = yzhGoodsList.stream()
                    .map(YZHGoodsSkuDTO::getGoodsSkuCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 查询已存在的商品SKU（包含完整信息）
            Map<String, GoodsSku> existingSkuMap = getExistingGoodsSkuMap(skuCodes);
            classification.setExistingSkuMap(existingSkuMap);

            // 分类商品数据
            for (YZHGoodsSkuDTO goods : yzhGoodsList) {
                if (existingSkuMap.containsKey(goods.getGoodsSkuCode())) {
                    classification.getUpdateGoods().add(goods);
                } else {
                    classification.getNewGoods().add(goods);
                }
            }

            log.info("商品数据分类完成，总数: {}, 新增: {}, 更新: {}",
                    yzhGoodsList.size(), classification.getNewGoods().size(), classification.getUpdateGoods().size());

            return classification;

        } catch (Exception e) {
            log.error("商品数据分类时发生异常: {}", e.getMessage(), e);
            // 异常时将所有数据归类为新增，避免数据丢失
            classification.setNewGoods(yzhGoodsList);
            return classification;
        }
    }

    /**
     * 获取已存在的商品SKU映射
     * 使用 supplierGoodsId 字段进行比对
     *
     * @param skuCodes SKU编码列表
     * @return SKU编码到GoodsSku的映射
     */
    private Map<String, GoodsSku> getExistingGoodsSkuMap(List<String> skuCodes) {
        try {
            if (skuCodes == null || skuCodes.isEmpty()) {
                return new HashMap<>();
            }

            // 查询已存在的商品SKU（使用 supplierGoodsId 字段）
            List<GoodsSku> existingSkus = goodsSkuService.list(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GoodsSku>()
                            .in(GoodsSku::getSupplierGoodsId, skuCodes)
                            .eq(GoodsSku::getSupplierId, SupplierEnum.YZH.getDefaultId())
                            .eq(GoodsSku::getDeleteFlag, false)
            );

            // 构建映射：云中鹤SKU编码 -> GoodsSku
            Map<String, GoodsSku> skuMap = new HashMap<>();
            for (GoodsSku sku : existingSkus) {
                if (sku.getSupplierGoodsId() != null) {
                    skuMap.put(sku.getSupplierGoodsId(), sku);
                }
            }

            log.debug("查询到已存在的商品SKU数量: {} / {}", skuMap.size(), skuCodes.size());
            return skuMap;

        } catch (Exception e) {
            log.error("获取已存在商品SKU映射时发生异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 处理新增商品
     */
    private int processNewGoods(List<YZHGoodsSkuDTO> newGoods, String userId, String scene, String extendId, int batchNum) {
        try {
            YzhGoodsConvertService.ConvertResult convertResult =
                    yzhGoodsConvertService.batchConvert(newGoods, userId, scene, extendId);

            List<Goods> goodsList = convertResult.getGoodsList();
            List<GoodsSku> goodsSkuList = convertResult.getGoodsSkuList();

            if (!goodsList.isEmpty() && !goodsSkuList.isEmpty()) {
                boolean goodsSaved = goodsService.saveBatch(goodsList);
                boolean skuSaved = goodsSkuService.saveBatch(goodsSkuList);

                if (goodsSaved && skuSaved) {
                    log.info("第 {} 批新增商品保存成功: {}", batchNum, goodsSkuList.size());
                    return goodsSkuList.size();
                }
            }

            log.error("第 {} 批新增商品保存失败", batchNum);
            return 0;
        } catch (Exception e) {
            log.error("第 {} 批处理新增商品异常: {}", batchNum, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 处理更新商品
     */
    private int processUpdateGoods(List<YZHGoodsSkuDTO> updateGoods, String userId, String scene, String extendId, int batchNum) {
        int successCount = 0;
        for (YZHGoodsSkuDTO yzhGoods : updateGoods) {
            if (updateSingleGoods(yzhGoods, userId, scene, extendId)) {
                successCount++;
            }
        }
        log.info("第 {} 批更新商品完成: {} / {}", batchNum, successCount, updateGoods.size());
        return successCount;
    }

    /**
     * 更新单个商品
     */
    private boolean updateSingleGoods(YZHGoodsSkuDTO yzhGoods, String userId, String scene, String extendId) {
        try {
            // 查询现有商品SKU（使用 supplierGoodsId 字段）
            GoodsSku existingSku = goodsSkuService.getOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GoodsSku>()
                            .eq(GoodsSku::getSupplierGoodsId, yzhGoods.getGoodsSkuCode())
                            .eq(GoodsSku::getSupplierId, SupplierEnum.YZH.getDefaultId())
                            .eq(GoodsSku::getDeleteFlag, false)
            );

            if (existingSku == null) return false;

            Goods existingGoods = goodsService.getById(existingSku.getGoodsId());
            if (existingGoods == null) return false;

            // 转换新数据
            YzhGoodsConvertService.ConvertResult convertResult =
                    yzhGoodsConvertService.batchConvert(List.of(yzhGoods), userId, scene, extendId);

            if (convertResult.getGoodsList().isEmpty() || convertResult.getGoodsSkuList().isEmpty()) {
                return false;
            }

            // 更新字段
            updateGoodsFields(existingGoods, convertResult.getGoodsList().get(0));
            updateGoodsSkuFields(existingSku, convertResult.getGoodsSkuList().get(0));

            // 执行更新
            return goodsService.updateById(existingGoods) && goodsSkuService.updateById(existingSku);

        } catch (Exception e) {
            log.error("更新商品异常，SKU: {}", yzhGoods.getGoodsSkuCode(), e);
            return false;
        }
    }

    /**
     * 更新商品字段
     */
    private void updateGoodsFields(Goods existingGoods, Goods newGoods) {
        existingGoods.setGoodsName(newGoods.getGoodsName());
        existingGoods.setSellingPoint(newGoods.getSellingPoint());
        existingGoods.setIntro(newGoods.getIntro());
        existingGoods.setMobileIntro(newGoods.getMobileIntro());
        existingGoods.setGoodsVideo(newGoods.getGoodsVideo());
        existingGoods.setOriginal(newGoods.getOriginal());
        existingGoods.setSmall(newGoods.getSmall());
        existingGoods.setThumbnail(newGoods.getThumbnail());
        existingGoods.setGoodsUnit(newGoods.getGoodsUnit());
        existingGoods.setParams(newGoods.getParams());
        existingGoods.setCategoryPath(newGoods.getCategoryPath());
        existingGoods.setBrandId(newGoods.getBrandId());
        existingGoods.setOriginalPrice(newGoods.getOriginalPrice());
        existingGoods.setPlatformPrice(newGoods.getPlatformPrice());
        existingGoods.setUpdateTime(new java.util.Date());
    }

    /**
     * 更新商品SKU字段
     */
    private void updateGoodsSkuFields(GoodsSku existingSku, GoodsSku newSku) {
        existingSku.setGoodsName(newSku.getGoodsName());
        existingSku.setSellingPoint(newSku.getSellingPoint());
        existingSku.setIntro(newSku.getIntro());
        existingSku.setMobileIntro(newSku.getMobileIntro());
        existingSku.setGoodsVideo(newSku.getGoodsVideo());
        existingSku.setOriginal(newSku.getOriginal());
        existingSku.setSmall(newSku.getSmall());
        existingSku.setThumbnail(newSku.getThumbnail());
        existingSku.setGoodsUnit(newSku.getGoodsUnit());
        existingSku.setPrice(newSku.getPrice());
        existingSku.setCost(newSku.getCost());
        existingSku.setQuantity(newSku.getQuantity());
        existingSku.setCategoryPath(newSku.getCategoryPath());
        existingSku.setBrandId(newSku.getBrandId());
        existingSku.setSpecs(newSku.getSpecs());
        existingSku.setSimpleSpecs(newSku.getSimpleSpecs());
        existingSku.setUpdateTime(new java.util.Date());
    }

    // ==================== 公共重复性检查方法 ====================

    @Override
    public boolean isGoodsExists(String skuCode) {
        return checkGoodsExists(skuCode);
    }

    @Override
    public Set<String> getExistingSkuCodes(List<String> skuCodes) {
        return getExistingYzhSkuCodes(skuCodes);
    }

    // ==================== 私有工具方法 ====================

    /**
     * 批量查询已存在的云中鹤SKU编码
     * 使用 supplierGoodsId 字段进行比对
     */
    private Set<String> getExistingYzhSkuCodes(List<String> skuCodes) {
        if (skuCodes == null || skuCodes.isEmpty()) {
            return new HashSet<>();
        }

        try {
            List<GoodsSku> existingSkus = goodsSkuService.list(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GoodsSku>()
                            .select(GoodsSku::getSupplierGoodsId)
                            .in(GoodsSku::getSupplierGoodsId, skuCodes)
                            .eq(GoodsSku::getSupplierId, SupplierEnum.YZH.getDefaultId())
                            .eq(GoodsSku::getDeleteFlag, false)
            );

            return existingSkus.stream()
                    .map(GoodsSku::getSupplierGoodsId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("查询已存在SKU编码异常: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    /**
     * 检查单个商品是否已存在
     * 使用 supplierGoodsId 字段进行比对
     */
    private boolean checkGoodsExists(String skuCode) {
        if (skuCode == null || skuCode.trim().isEmpty()) {
            return false;
        }
        return goodsSkuService.count(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GoodsSku>()
                        .eq(GoodsSku::getSupplierGoodsId, skuCode)
                        .eq(GoodsSku::getSupplierId, SupplierEnum.YZH.getDefaultId())
                        .eq(GoodsSku::getDeleteFlag, false)
        ) > 0;
    }



    /**
     * 获取所有商品数据（分页获取但不保存）
     */
    private List<YZHGoodsSkuDTO> fetchAllGoodsData(String accessToken, GoodsInitProgressDTO progress, String progressKey) {
        List<YZHGoodsSkuDTO> allGoodsList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = PAGE_SIZE;
        int totalPages = 0;

        try {
            while (true) {
                // 更新获取进度 (10%-40%范围内)
                int fetchProgress = Math.min(40, 10 + (pageNum - 1) * 2);
                updateProgress(progress, progressKey, fetchProgress,
                             String.format("正在获取第 %d 页商品数据...", pageNum), null);

                log.info("开始获取第 {} 页商品数据，每页 {} 条", pageNum, pageSize);

                // 获取单页数据
                YZHGoodsResultDTO result = fetchSinglePageGoods(accessToken, pageNum, pageSize);

                if (result == null || result.getGoodsSkuList() == null || result.getGoodsSkuList().isEmpty()) {
                    log.info("第 {} 页商品数据为空，结束分页获取", pageNum);
                    break;
                }

                log.info("第 {} 页获取到 {} 条商品数据", pageNum, result.getGoodsSkuList().size());

                // 获取商品详情
                List<String> skuCodes = result.getGoodsSkuList().stream()
                        .map(YZHGoodsSkuDTO::getGoodsSkuCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                List<YZHGoodsSkuDTO> detailedGoodsList = fetchGoodsDetailsBySkuCodes(accessToken, skuCodes);
                allGoodsList.addAll(detailedGoodsList);

                // 检查是否还有更多页
                if (result.getGoodsSkuList().size() < pageSize) {
                    log.info("当前页数据量小于页大小，已获取完所有数据");
                    break;
                }

                pageNum++;
                totalPages = pageNum - 1;
            }

            log.info("商品数据获取完成，总页数: {}, 总商品数: {}", totalPages, allGoodsList.size());
            return allGoodsList;

        } catch (Exception e) {
            log.error("获取商品数据时发生异常: {}", e.getMessage(), e);
            return allGoodsList; // 返回已获取的数据
        }
    }

    /**
     * 带进度的分类映射处理
     */
    private void processAllCategoryMappingsWithProgress(List<YZHGoodsSkuDTO> allGoodsList,
                                                       GoodsInitProgressDTO progress, String progressKey,
                                                       int startProgress, int progressRange) {
        try {
            updateProgress(progress, progressKey, startProgress + 2, "正在加载系统分类数据...", null);
            List<CategoryVO> systemCategories = categoryService.categoryTree();

            updateProgress(progress, progressKey, startProgress + 5, "正在处理分类映射关系...", null);
            processAndSaveAllCategoryMappings(allGoodsList, systemCategories);

            updateProgress(progress, progressKey, startProgress + progressRange, "分类映射处理完成", null);
            log.info("分类映射处理完成");

        } catch (Exception e) {
            log.error("处理分类映射时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 带进度的新增商品处理
     */
    private int processNewGoodsWithProgress(List<YZHGoodsSkuDTO> newGoods, String userId, String scene, String extendId,
                                          GoodsInitProgressDTO progress, String progressKey,
                                          int startProgress, int progressRange) {
        if (newGoods.isEmpty()) {
            updateProgress(progress, progressKey, startProgress + progressRange, "无新增商品需要处理", null);
            return 0;
        }

        try {
            int totalCount = newGoods.size();
            int processedCount = 0;
            int batchSize = BATCH_SIZE;

            log.info("开始处理新增商品，总数: {}", totalCount);

            for (int i = 0; i < newGoods.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, newGoods.size());
                List<YZHGoodsSkuDTO> batchGoods = newGoods.subList(i, endIndex);

                // 计算当前批次的进度
                int currentProgress = startProgress + (int)((double)processedCount / totalCount * progressRange);
                updateProgress(progress, progressKey, currentProgress,
                             String.format("正在保存新增商品 %d/%d...", processedCount + batchGoods.size(), totalCount), null);

                YzhGoodsConvertService.ConvertResult convertResult =
                        yzhGoodsConvertService.batchConvert(batchGoods, userId, scene, extendId);

                List<Goods> goodsList = convertResult.getGoodsList();
                List<GoodsSku> goodsSkuList = convertResult.getGoodsSkuList();

                if (!goodsList.isEmpty() && !goodsSkuList.isEmpty()) {
                    boolean goodsSaved = goodsService.saveBatch(goodsList);
                    boolean skuSaved = goodsSkuService.saveBatch(goodsSkuList);

                    if (goodsSaved && skuSaved) {
                        processedCount += batchGoods.size();
                        log.info("新增商品批次保存成功: {}/{}", processedCount, totalCount);
                    }
                }
            }

            updateProgress(progress, progressKey, startProgress + progressRange,
                         String.format("新增商品处理完成: %d/%d", processedCount, totalCount), null);

            log.info("新增商品处理完成，成功: {}/{}", processedCount, totalCount);
            return processedCount;

        } catch (Exception e) {
            log.error("处理新增商品时发生异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 带进度的更新商品处理
     */
    private int processUpdateGoodsWithProgress(List<YZHGoodsSkuDTO> updateGoods, String userId, String scene, String extendId,
                                             GoodsInitProgressDTO progress, String progressKey,
                                             int startProgress, int progressRange) {
        if (updateGoods.isEmpty()) {
            updateProgress(progress, progressKey, startProgress + progressRange, "无商品需要更新", null);
            return 0;
        }

        try {
            int totalCount = updateGoods.size();
            int processedCount = 0;

            log.info("开始处理更新商品，总数: {}", totalCount);

            for (int i = 0; i < updateGoods.size(); i++) {
                YZHGoodsSkuDTO yzhGoods = updateGoods.get(i);

                // 计算当前进度
                int currentProgress = startProgress + (int)((double)i / totalCount * progressRange);
                if (i % 10 == 0) { // 每10个更新一次进度，避免过于频繁
                    updateProgress(progress, progressKey, currentProgress,
                                 String.format("正在更新商品 %d/%d...", i + 1, totalCount), null);
                }

                if (updateSingleGoods(yzhGoods, userId, scene, extendId)) {
                    processedCount++;
                }
            }

            updateProgress(progress, progressKey, startProgress + progressRange,
                         String.format("商品更新完成: %d/%d", processedCount, totalCount), null);

            log.info("商品更新处理完成，成功: {}/{}", processedCount, totalCount);
            return processedCount;

        } catch (Exception e) {
            log.error("处理更新商品时发生异常: {}", e.getMessage(), e);
            return 0;
        }
    }
}
