package plus.qdt.controller.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.common.utils.DateUtil;
import plus.qdt.modules.domain.StoreAmount;
import plus.qdt.modules.domain.UserAmount;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import plus.qdt.modules.jinTongAccount.client.AmountClient;
import plus.qdt.modules.jinTongAccount.entity.vo.StoreAmountVo;
import plus.qdt.modules.wallet.mapper.UserAmountMapper;
import plus.qdt.modules.wallet.service.StoreAmountService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2.0
 */
@RestController
@RequiredArgsConstructor
public class AmountFeignController implements AmountClient {

    private final UserAmountMapper amountMapper;

    private final StoreAmountService storeAmountService;

    private final GoodsClient goodsClient;

    @Override
    public void openAmount(String userId) {
        UserAmount amount = new UserAmount();
        amount.setId(userId);
        amountMapper.insert(amount);
    }

    @Override
    public BigDecimal getAmount(String userId) {
        return amountMapper.queryAccount(userId);
    }

    @Override
    public BigDecimal getQiAmount(String userId) {
        return amountMapper.queryQiAccountForLock(userId);
    }

    @Override
    public void openStoreAmount(String storeId) {
        StoreAmount amount = new StoreAmount();
        amount.setId(storeId);
        storeAmountService.save(amount);
    }

    @Override
    public BigDecimal getStoreAmount(String storeId) {
        return storeAmountService.getById(storeId).getCaiTongCoin();
    }

    @Override
    public void withdrawSet(String storeId) {
        StoreAmount amount = storeAmountService.getById(storeId);
        amount.setWithdraw(true);
        if (amount.getWithdrawDate() != null) {
            // 已过期，则一当前时间往后推一年
            if (amount.getWithdrawDate().before(new Date())) {
                amount.setWithdrawDate(DateUtil.offsetDay(new Date(), 365));
            } else {
                // 未过期，则以年卡时间往后推一年
                amount.setWithdrawDate(DateUtil.offsetDay(amount.getWithdrawDate(), 365));
            }
        } else {
            // 没有时间，则添加一年时间
            amount.setWithdrawDate(DateUtil.offsetDay(new Date(), 365));
        }
        storeAmountService.updateById(amount);
        // 上架商品
        goodsClient.updateGoodsMarket(storeId, GoodsMarketEnum.UPPER);
    }

    @Override
    public boolean withdraw(String storeId) {
        StoreAmount amount = storeAmountService.getById(storeId);
        return amount.isAllowWithdraw();
    }

    @Override
    public StoreAmountVo getStoreInfo(String storeId, String date) {
        return storeAmountService.getStoreInfo(storeId, date);
    }

    @Override
    public List<String> getAllVipStoreId() {
        return storeAmountService.listObjs(Wrappers.<StoreAmount>lambdaQuery()
                .eq(StoreAmount::getWithdraw, true)
                .isNotNull(StoreAmount::getWithdrawDate).select(StoreAmount::getId), Object::toString);
    }
}
