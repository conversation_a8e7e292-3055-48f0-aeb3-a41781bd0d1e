package plus.qdt.modules.wallet.config;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import plus.qdt.cache.Cache;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.annotation.NoVerify;

/**
 * 账户资产拦截器
 *
 * <AUTHOR>
 * @since 2.0
 */
@Configuration
public class WalletMvcConfiguration implements WebMvcConfigurer {

    @Resource
    private Cache<Object> cache;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
                if (handler instanceof HandlerMethod handlerMethod) {
                    NoVerify annotation = handlerMethod.getMethod().getAnnotation(NoVerify.class);
                    if (annotation != null) {
                        return true;
                    }
                }
                AuthUser authUser = UserContext.getCurrentExistUser();
                String key = "account:verify:" + authUser.getScene().name() + ":" + authUser.getId();
                if (!cache.hasKey(key)) {
                    throw new ServiceException(ResultCode.PAY_ACCOUNT_VERIFY_EXPIRE);
                }
                // 如果在访问的接口中，则延长过期时间
                cache.put(key, "true", 300L);
                return true;
            }
        }).addPathPatterns("/account/amount/**");
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
                if (handler instanceof HandlerMethod handlerMethod) {
                    NoVerify annotation = handlerMethod.getMethod().getAnnotation(NoVerify.class);
                    if (annotation != null) {
                        return true;
                    }
                }
                AuthUser authUser = UserContext.getCurrentExistUser();
                // 校验场景值
                if (!SceneEnums.STORE.equals(authUser.getScene())) {
                    throw new ServiceException(ResultCode.SCENE_NOT_SUPPORT);
                }
                String key = "account:verify:" + authUser.getScene().name() + ":" + authUser.getExtendId();
                if (!cache.hasKey(key)) {
                    throw new ServiceException(ResultCode.PAY_ACCOUNT_VERIFY_EXPIRE);
                }
                // 如果在访问的接口中，则延长过期时间
                cache.put(key, "true", 1800L);
                return true;
            }
        }).addPathPatterns("/account/amountStore/**");
    }
}
