package plus.qdt.modules.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.math.BigDecimal;

/**
 * 金通宝账号;
 *
 * <AUTHOR> yegy
 * @date : 2025-4-28
 */
@Data
@TableName("qdt_jintong_account")
@EqualsAndHashCode(callSuper = true)
public class JinTongAccount extends BaseStandardEntity {
    /**
     * 金通宝金额
     */
    private BigDecimal jtAccount;
    /**
     * 借贷池
     */
    private BigDecimal loanPool;
    /**
     * 还贷池
     */
    private BigDecimal repayPool;
    /**
     * 分红池
     */
    private BigDecimal dividendPool;
    /**
     * 预警金额，如果金通宝金额低于这个资金就发消息告警
     */
    private BigDecimal jtAccountEarlyWarning;
    /**
     * 借贷池预警值
     */
    private BigDecimal loanPoolEarlyWarning;
}