package plus.qdt.modules.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import plus.qdt.modules.domain.StoreAmount;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2.0
 */
public interface StoreAmountMapper extends BaseMapper<StoreAmount> {

    /**
     * 获取商家财通余额
     * @param id 商家ID
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal queryAccountForLock(String id);

    /**
     * 商家财通总额
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal sumMoney();

}
