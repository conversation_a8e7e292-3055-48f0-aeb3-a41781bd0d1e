package plus.qdt.modules.order.order.serviceimpl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.cache.Cache;
import plus.qdt.cache.CachePrefix;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.GsonUtils;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.domain.dto.AmountTypeDto;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.enums.GoodsType;
import plus.qdt.modules.goods.entity.enums.GoodsTypeEnum;
import plus.qdt.modules.jinTongAccount.client.AccountConsumeClient;
import plus.qdt.modules.jinTongAccount.client.AmountClient;
import plus.qdt.modules.jinTongAccount.entity.AccountConsume;
import plus.qdt.modules.jinTongAccount.enums.*;
import plus.qdt.modules.member.client.TeamClient;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.dos.UserAddress;
import plus.qdt.modules.member.entity.enums.PromotionGrade;
import plus.qdt.modules.order.cart.entity.dto.MemberCouponDTO;
import plus.qdt.modules.order.cart.entity.dto.TradeCouponDTO;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dos.Trade;
import plus.qdt.modules.order.order.entity.enums.OrderStatusEnum;
import plus.qdt.modules.order.order.entity.vo.TradeDetailVO;
import plus.qdt.modules.order.order.mapper.TradeMapper;
import plus.qdt.modules.order.order.service.OrderItemService;
import plus.qdt.modules.order.order.service.OrderService;
import plus.qdt.modules.order.order.service.QdtDigitalAgencyService;
import plus.qdt.modules.order.order.service.TradeService;
import plus.qdt.modules.payment.client.WalletPointClient;
import plus.qdt.modules.payment.entity.dos.CombinePaymentLog;
import plus.qdt.modules.payment.entity.dos.PaymentLog;
import plus.qdt.modules.payment.entity.dto.PaymentCallback;
import plus.qdt.modules.payment.entity.dto.UserPointUpdateDTO;
import plus.qdt.modules.payment.entity.enums.PaymentMethodEnum;
import plus.qdt.modules.payment.entity.enums.UserPointServiceEnum;
import plus.qdt.modules.promotion.client.KanjiaActivityClient;
import plus.qdt.modules.promotion.client.MemberCouponClient;
import plus.qdt.modules.promotion.client.PromotionsClient;
import plus.qdt.modules.store.client.StoreClient;
import plus.qdt.modules.store.entity.vos.StoreVO;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dto.QdtPaySetting;
import plus.qdt.mybatis.util.SceneHelp;
import plus.qdt.routing.OrderRoutingKey;
import plus.qdt.util.AmqpMessage;
import plus.qdt.util.AmqpSender;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 交易业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:39 下午
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TradeServiceImpl extends ServiceImpl<TradeMapper, Trade> implements TradeService {

    private final Cache<Object> cache;
    private final OrderService orderService;
    private final OrderItemService orderItemService;
    private final MemberCouponClient memberCouponClient;
    private final PromotionsClient promotionsClient;
    private final WalletPointClient walletPointClient;
    private final KanjiaActivityClient kanjiaActivityClient;
    private final AmqpSender amqpSender;
    private final AmqpExchangeProperties amqpExchangeProperties;
    private final AccountConsumeClient accountConsumeClient;
    private final AmountClient amountClient;
    private final GoodsClient goodsClient;
    private final UserClient userClient;
    private final StoreClient storeClient;
    private final TeamClient teamClient;
    private final QdtDigitalAgencyService digitalAgencyService;
    private final SettingClient settingClient;


    @Override
    @Transactional
    public Trade createTrade(TradeDTO tradeDTO) {

        //创建订单预校验
        createTradeCheck(tradeDTO);


        Trade trade = new Trade(tradeDTO);
        String key = CachePrefix.TRADE.getPrefix() + trade.getSn();
        //交易预处理
        pretreatment(tradeDTO);
        //添加交易
        this.save(trade);
        //添加订单
        orderService.intoDB(tradeDTO);
        //写入缓存，给消费者调用
        // 0402 hutool json 转换 有问题 无法转换 flowPrice 为 0 的情况 会报错 暂时用gson
        cache.put(key, GsonUtils.toJson(tradeDTO));

        //构建订单创建消息
        amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getOrder()).routingKey(OrderRoutingKey.ORDER_CREATE).message(key).build());

        //
        return trade;
    }

    @Override
    public Trade getBySn(String sn) {
        return this.baseMapper.getBySn(sn);
    }

    @Override
    public Trade getTrade(String tradeSn, AuthUser authUser) {
        LambdaQueryWrapper<Trade> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Trade::getSn, tradeSn);
        // 只过滤会员被黑进行支付
        if (authUser.getScene().equals(SceneEnums.MEMBER)) {
            queryWrapper.eq(Trade::getMemberId, authUser.getId());
        }
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payTrade(PaymentCallback paymentCallback) {
        log.info("\n支付业务处理: {}", JSONUtil.toJsonStr(paymentCallback));
        if (Boolean.TRUE.equals(paymentCallback.getIsCombine())) {
            //对订单做处理
            for (PaymentLog paymentLog : paymentCallback.getPaymentLogs()) {
                orderService.payOrder(paymentLog);
            }
            CombinePaymentLog combinePaymentLog = paymentCallback.getCombinePaymentLog();
            //修改交易信息
            Trade trade = this.getBySn(combinePaymentLog.getOrderSn());
            trade.setOrderStatus(OrderStatusEnum.PAID.name());
            trade.setPaymentMethod(combinePaymentLog.getPaymentMethod());
            trade.setReceivableNo(combinePaymentLog.getCombineOutTradeNo());
            // 财通支付设置财通金额
            if (PaymentMethodEnum.COIN.name().equals(combinePaymentLog.getPaymentMethod())) {
                trade.setCaiTongCoin(trade.getFlowPrice());
            }
            this.updateById(trade);
            // 合单只能是商品
            this.qdtTrade(trade, new AmountTypeDto(JinTongAccountLogEnums.USER_BUY_GOODS,
                    UserAmountLogEnum.DESTRUCTION, CaiTongAccountLogEnum.USER_BUY_GOODS));
        } else {
            Order order = orderService.payOrder(paymentCallback.getPaymentLog());
            //否则根据支付日志进行处理
            PaymentLog paymentLog = paymentCallback.getPaymentLog();
            //修改交易信息
            Trade trade = this.getBySn(order.getTradeSn());
            List<OrderItem> orderItems = orderItemService.getByOrderSn(order.getSn());
            // 获取商品项
            AmountTypeDto amountType = new AmountTypeDto(JinTongAccountLogEnums.USER_BUY_GOODS,
                    UserAmountLogEnum.DESTRUCTION, CaiTongAccountLogEnum.USER_BUY_GOODS);
            // 购买单件商品，判断是否为特殊的虚拟自建商品：年卡、创业礼包、数商礼包
            if (orderItems.size() == 1) {
                amountType = this.handlerGoods(orderItems.getFirst().getGoodsId(), trade.getMemberId(), order.getSn());
            }
            if (amountType.isVirtual()) {
                trade.setOrderStatus(OrderStatusEnum.COMPLETED.name());
                order.setOrderStatus(OrderStatusEnum.COMPLETED.name());
                // 虚拟商品订单完成
                orderService.updateById(order);
            } else {
                trade.setOrderStatus(OrderStatusEnum.PAID.name());
            }
            trade.setPaymentMethod(paymentLog.getPaymentMethod());
            trade.setReceivableNo(paymentLog.getOutTradeNo());
            // 财通支付设置财通金额
            if (PaymentMethodEnum.COIN.name().equals(paymentLog.getPaymentMethod())) {
                trade.setCaiTongCoin(trade.getFlowPrice());
            }
            this.updateById(trade);
            // 获取订单信息
            this.qdtTrade(trade, amountType);
        }
    }

    /**
     * 处理用户购买的商品/年卡/数商代理礼包
     * @param goodsId 商品ID
     * @return {@link JinTongAccountLogEnums}
     * <AUTHOR>
     */
    private AmountTypeDto handlerGoods(String goodsId, String userId, String orderId) {
        AmountTypeDto amountType = new AmountTypeDto(JinTongAccountLogEnums.USER_BUY_GOODS,
                UserAmountLogEnum.DESTRUCTION, CaiTongAccountLogEnum.USER_BUY_GOODS);
        // 获取商品信息
        Goods goods = goodsClient.getById(goodsId);
        // 在生产订单流水时判断是否为会员卡
        if (goods != null) {
            String categoryPath = goods.getCategoryPath();
            if (StringUtils.isNotBlank(categoryPath)) {
                // 订单类型
                String[] categories = categoryPath.split(",");
                // 订单真实类型
                String category = categories[categories.length - 1];
                // 如果是会员年卡
                if (GoodsType.VIP_CARDS.getCategoryId().equals(category) ||
                        GoodsType.ENTREPRENEURIAL_GIFT_PACK.getCategoryId().equals(category)) {
                    // 新增vip/续费vip -如果是创业者礼包则赠送年卡
                    userClient.openVip(userId);
                    // 购买年卡修改为创业者
                    teamClient.updatePromotion(userId, PromotionGrade.ENTREPRENEURS);
                    if (GoodsType.VIP_CARDS.getCategoryId().equals(category)) {
                        amountType.setJinTong(JinTongAccountLogEnums.USER_BUY_ANNUAL_CARD);
                        amountType.setUser(UserAmountLogEnum.BUY_VIP);
                        amountType.setCaiTong(CaiTongAccountLogEnum.USER_BUY_VIP);
                        amountType.setVirtual(true);
                    } else {
                        amountType.setJinTong(JinTongAccountLogEnums.USER_BUY_START_BUSINESS);
                        amountType.setUser(UserAmountLogEnum.BUY_VIP_PACK);
                        amountType.setCaiTong(CaiTongAccountLogEnum.USER_BUY_ENTERPRISE);
                    }
                } else if (GoodsType.STORE_YEAR_FEE.getCategoryId().equals(category)) {
                    // 商家年费
                    amountClient.withdrawSet(userId);
                    amountType.setVirtual(true);
                    amountType.setJinTong(JinTongAccountLogEnums.SHOP_BUY_ANNUAL_FEE);
                    amountType.setStore(StoreAmountLogEnum.VIP);
                    amountType.setCaiTong(CaiTongAccountLogEnum.SHOP_BUY_ANNUAL_FEE);
                } else if (GoodsType.VILLAGE_GIFT_PACK.getCategoryId().equals(category)) {
                    // 激活代理
                    digitalAgencyService.activeProxy(orderId);
                    // 设置被推广人为村代
                    teamClient.updateGift(userId, 1);
                    amountType.setJinTong(JinTongAccountLogEnums.USER_BUY_GIFT_VILLAGE);
                    amountType.setUser(UserAmountLogEnum.BUY_VILLAGE_PACK);
                    amountType.setCaiTong(CaiTongAccountLogEnum.USER_BUY_VILLAGE);
                } else if (GoodsType.STREET_GIFT_PACK.getCategoryId().equals(category)) {
                    // 激活代理
                    digitalAgencyService.activeProxy(orderId);
                    // 设置被推广人为镇代
                    teamClient.updateGift(userId, 2);
                    amountType.setJinTong(JinTongAccountLogEnums.USER_BUY_GIFT_TOWN);
                    amountType.setUser(UserAmountLogEnum.BUY_STREET_PACK);
                    amountType.setCaiTong(CaiTongAccountLogEnum.USER_BUY_GIFT_TOWN);
                } else if (GoodsType.AREA_GIFT_PACK.getCategoryId().equals(category)) {
                    // 激活代理
                    digitalAgencyService.activeProxy(orderId);
                    // 设置被推广人为县代
                    teamClient.updateGift(userId, 3);
                    amountType.setJinTong(JinTongAccountLogEnums.USER_BUY_GIFT_PREFECTURE);
                    amountType.setUser(UserAmountLogEnum.BUY_AREA_PACK);
                    amountType.setCaiTong(CaiTongAccountLogEnum.USER_BUY_GIFT_PREFECTURE);
                } 
            }
        }
        return amountType;
    }

    /**
     * 企通支付(第三方)执行逻辑
     * 交易完成 扣减金通宝 平台入账财通宝 用户财通扣减
     * @param trade 交易信息
     * @param amountType 子类型
     * <AUTHOR>
     */
    private void qdtTrade(Trade trade, AmountTypeDto amountType) {
        BigDecimal payMoney = BigDecimal.valueOf(trade.getFlowPrice());
        // 如果是组合支付,则使用总金额-余额
        boolean combination = trade.isCombination();
        if (combination) {
            // 现金金额
            payMoney = BigDecimal.valueOf(trade.getFlowPrice() - trade.getCaiTongCoin());
        }
        // 只有现金+组合支付 金通才出账
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.paymentNameOf(trade.getPaymentMethod());
        if (PaymentMethodEnum.WECHAT.equals(paymentMethod) || combination) {
            AccountConsume jinTongConsume = new AccountConsume()
                    .setId(trade.getMemberId())
                    .setNames(List.of(trade.getMemberName()))
                    .setMoney(payMoney)
                    .setAccountTypeEnum(AccountTypeEnum.JIN_TONG_ACCOUNT) // 金通宝
                    .setSubType(amountType.getJinTong().getSubType()); // 交易方式
            accountConsumeClient.jinTongAccountConsume(jinTongConsume);
        } else {
            payMoney = BigDecimal.ZERO;
        }
        // 平台财通入账-托管
        AccountConsume caiTongConsume = new AccountConsume()
                .setId(trade.getMemberId())
                .setNames(List.of(trade.getMemberName()))
                .setMoney(BigDecimal.valueOf(trade.getFlowPrice()))
                .setPayment(trade.getSn(), combination ? PayMethodEnum.CASH_COIN : PayMethodEnum.CASH, payMoney, BigDecimal.valueOf(trade.getCaiTongCoin()))
                .setSubType(amountType.getCaiTong().getSubType())
                .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT);
        accountConsumeClient.jinTongAccountConsume(caiTongConsume);

        /*     入账平台财通->分红->城市合伙人、直推人、数商       */

        // 购买了年卡给城市合伙人分红
        if (UserAmountLogEnum.BUY_VIP.equals(amountType.getUser())) {
            // 获取所有城市合伙人用户ID
            List<String> list = userClient.listCityPartner();
            if (!list.isEmpty()) {
                // 计算年卡分红总金额
                BigDecimal feeMoney = BigDecimal.valueOf(trade.getFlowPrice()).multiply(settingClient.getQdtPay().getCityVipFee());
                // 给城市合伙人分红
                BigDecimal divide = feeMoney.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
                for (String cityUserId : list) {
                    User cityPartner = userClient.getById(cityUserId);
                    if (cityPartner != null && cityPartner.isRealVip()) {
                        // 平台财通出账
                        accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                                .setId(cityUserId)
                                .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                                .setSubType(CaiTongAccountLogEnum.PARTNER_VIP_BONUS.getSubType())
                                .setNames(List.of(cityPartner.getNickName(), trade.getMemberName()))
                                .setMoney(divide)
                        );
                        // 用户财通入账
                        accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                                .setId(cityUserId)
                                .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                                .setSubType(UserAmountLogEnum.USERS_CARD_BONUS.getSubType())
                                .setNames(List.of(trade.getMemberName()))
                                .setMoney(divide)
                        );
                    }
                }
            }
            // 购买人信息
            User user = userClient.getById(trade.getMemberId());
            // 用户选择地址后才会有数商分红
            if (StringUtils.isNotBlank(user.getAreaCode())) {
                // 数商分红
                this.digitalBonus(user.getAreaCode(), trade.getMemberName(), trade.getFlowPrice());
            }
        } else if (amountType.getUser().isPack()) {
            // 如果是购买数商代理，则奖励直推用户
            User user = userClient.getShareInfo(trade.getMemberId());
            // 如果推广者是招商经理以上身份，则奖励直推用户
            if (user != null && user.getPromotionGrade().getGrade() >= PromotionGrade.INVESTMENT_MANAGER.getGrade() && user.isRealVip()) {
                // 计算奖励金额
                BigDecimal feeMoney = BigDecimal.valueOf(trade.getFlowPrice()).multiply(settingClient.getQdtPay().getDirectPushDigital());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(user.getId())
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.USER_BUY_VILLAGE_BONUS.getSubType())
                        .setNames(List.of(user.getNickName()))
                        .setMoney(feeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(user.getId())
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.USER_VILLAGE_BONUS.getSubType())
                        .setNames(List.of(trade.getMemberName()))
                        .setMoney(feeMoney)
                );
            }
        } else if (StoreAmountLogEnum.VIP.equals(amountType.getStore())) {
            // 直推人
            StoreVO store = storeClient.getStore(trade.getMemberId());
            // 直推人奖励
            User user = userClient.getShareInfo(store.getCreateBy());
            if (user != null && user.isRealVip()) {
                // 平台财通出账
                this.accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(user.getId())
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.SHOP_BUY_ANNUAL_FEE.getSubType())
                        .setNames(List.of(store.getStoreName()))
                        // 直推商家年费分红
                        .setMoney(BigDecimal.valueOf(settingClient.getQdtPay().getDirectPushStoreFee()))
                );
                // 用户财通入账
                this.accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(user.getId())
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.USER_SHOP_BONUS.getSubType())
                        .setNames(List.of(store.getStoreName()))
                        // 直推商家年费分红
                        .setMoney(BigDecimal.valueOf(settingClient.getQdtPay().getDirectPushStoreFee()))
                );
            }
            if (StringUtils.isNotBlank(store.getStoreArea())) {
                // 获取数商奖励
                this.storeVip(store.getStoreArea(), store.getStoreName(), trade.getFlowPrice());
            }
        }
        // 不等于财通支付，则出账
        if (!PaymentMethodEnum.COIN.name().equals(trade.getPaymentMethod())) {
            // 根据场景值获取账户类型
            AccountTypeEnum accountTypeEnum; Integer subType;
            if (SceneEnums.STORE.name().equals(trade.getScene())) {
                accountTypeEnum = AccountTypeEnum.STORE_CAI_TONG_ACCOUNT;
                subType = StoreAmountLogEnum.VIP.getSubType();
            } else {
                accountTypeEnum = AccountTypeEnum.USER_CAI_TONG_ACCOUNT;
                subType = amountType.getUser().getSubType();
            }
            if (combination) {
                // 如果是组合支付。则将财通出完
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(trade.getMemberId())
                        .setPayment(trade.getSn(), PayMethodEnum.CASH_COIN, payMoney, BigDecimal.valueOf(trade.getCaiTongCoin()))
                        .setAccountTypeEnum(accountTypeEnum)
                        .setSubType(subType)
                        .setMoney(BigDecimal.valueOf(trade.getFlowPrice()))
                );
            } else {
                // 用户财通账户出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(trade.getMemberId())
                        .setPayment(true) // 只记录出账日志，不操作账户金额
                        .setAccountTypeEnum(accountTypeEnum)
                        .setSubType(subType)
                        .setMoney(BigDecimal.valueOf(trade.getFlowPrice())) // 出账为当前订单总额
                );
            }
        }
    }

    /**
     * 商家年卡分红
     * @param userAreaCode 商家行政区划代码
     * @param nickname 商家名称
     * @param money 年卡金额
     * <AUTHOR>
     */
    private void storeVip(String userAreaCode, String nickname, Double money) {
        QdtPaySetting qdtPay = settingClient.getQdtPay();
        // 数商县代
        String areaUserId = digitalAgencyService.proxyUserId(userAreaCode, GoodsType.AREA_GIFT_PACK);
        if (StringUtils.isNotBlank(areaUserId)) {
            User user = userClient.getById(areaUserId);
            if (user != null && user.isRealVip()) {
                // 县代分红金额
                BigDecimal areaFeeMoney = BigDecimal.valueOf(money).multiply(qdtPay.getAreaStoreYearFee());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(areaUserId)
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.AREA_SHOP_VIP_BONUS.getSubType())
                        .setNames(List.of(user.getNickName(), nickname))
                        .setMoney(areaFeeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(areaUserId)
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.AREA_YEAR_BONUS.getSubType())
                        .setNames(List.of(nickname))
                        .setMoney(areaFeeMoney)
                );
            }
        }
        // 数商镇代
        String streetUserId = digitalAgencyService.proxyUserId(userAreaCode, GoodsType.STREET_GIFT_PACK);
        if (StringUtils.isNotBlank(streetUserId)) {
            User user = userClient.getById(streetUserId);
            if (user != null && user.isRealVip()) {
                // 镇代分红金额
                BigDecimal areaFeeMoney = BigDecimal.valueOf(money).multiply(qdtPay.getStreetStoreYearFee());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(streetUserId)
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.STREET_SHOP_VIP_BONUS.getSubType())
                        .setNames(List.of(user.getNickName(), nickname))
                        .setMoney(areaFeeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(streetUserId)
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.STREET_YEAR_BONUS.getSubType())
                        .setNames(List.of(nickname))
                        .setMoney(areaFeeMoney)
                );
            }
        }
        // 数商村代
        String villageUserId = digitalAgencyService.proxyUserId(userAreaCode, GoodsType.VILLAGE_GIFT_PACK);
        if (StringUtils.isNotBlank(villageUserId)) {
            User user = userClient.getById(villageUserId);
            if (user != null && user.isRealVip()) {
                // 村代分红金额
                BigDecimal areaFeeMoney = BigDecimal.valueOf(money).multiply(qdtPay.getVillageStoreYearFee());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(villageUserId)
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.TOWN_SHOP_VIP_BONUS.getSubType())
                        .setNames(List.of(user.getNickName(), nickname))
                        .setMoney(areaFeeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(villageUserId)
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.VILLAGE_YEAR_BONUS.getSubType())
                        .setNames(List.of(nickname))
                        .setMoney(areaFeeMoney)
                );
            }
        }
    }

    /**
     * 数商代理分红实现
     * @param userAreaCode 用户所属区域
     * @param nickname 用户昵称
     * @param money 商品金额
     * <AUTHOR>
     */
    private void digitalBonus(String userAreaCode, String nickname, Double money) {
        QdtPaySetting qdtPay = settingClient.getQdtPay();
        // 数商县代
        String areaUserId = digitalAgencyService.proxyUserId(userAreaCode, GoodsType.AREA_GIFT_PACK);
        if (StringUtils.isNotBlank(areaUserId)) {
            User user = userClient.getById(areaUserId);
            if (user != null && user.isRealVip()) {
                // 县代分红金额
                BigDecimal areaFeeMoney = BigDecimal.valueOf(money).multiply(qdtPay.getAreaVipFee());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(areaUserId)
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.AREA_VIP_BONUS.getSubType())
                        .setNames(List.of(user.getNickName(), nickname))
                        .setMoney(areaFeeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(areaUserId)
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.AREA_CARD_BONUS.getSubType())
                        .setNames(List.of(nickname))
                        .setMoney(areaFeeMoney)
                );
            }
        }
        // 数商镇代
        String streetUserId = digitalAgencyService.proxyUserId(userAreaCode, GoodsType.STREET_GIFT_PACK);
        if (StringUtils.isNotBlank(streetUserId)) {
            User user = userClient.getById(streetUserId);
            if (user != null && user.isRealVip()) {
                // 镇代分红金额
                BigDecimal areaFeeMoney = BigDecimal.valueOf(money).multiply(qdtPay.getStreetVipFee());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(streetUserId)
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.STREET_VIP_BONUS.getSubType())
                        .setNames(List.of(user.getNickName(), nickname))
                        .setMoney(areaFeeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(streetUserId)
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.STREET_CARD_BONUS.getSubType())
                        .setNames(List.of(nickname))
                        .setMoney(areaFeeMoney)
                );
            }
        }
        // 数商村代
        String villageUserId = digitalAgencyService.proxyUserId(userAreaCode, GoodsType.VILLAGE_GIFT_PACK);
        if (StringUtils.isNotBlank(villageUserId)) {
            User user = userClient.getById(villageUserId);
            if (user != null && user.isRealVip()) {
                // 村代分红金额
                BigDecimal areaFeeMoney = BigDecimal.valueOf(money).multiply(qdtPay.getVillageVipFee());
                // 平台财通出账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(villageUserId)
                        .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                        .setSubType(CaiTongAccountLogEnum.TOWN_VIP_BONUS.getSubType())
                        .setNames(List.of(user.getNickName(), nickname))
                        .setMoney(areaFeeMoney)
                );
                // 用户财通入账
                accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                        .setId(villageUserId)
                        .setAccountTypeEnum(AccountTypeEnum.USER_CAI_TONG_ACCOUNT)
                        .setSubType(UserAmountLogEnum.VILLAGE_CARD_BONUS.getSubType())
                        .setNames(List.of(nickname))
                        .setMoney(areaFeeMoney)
                );
            }
        }
    }

    @Override
    public void paymentCallbackZero(String sn) {
        Trade trade = this.getBySn(sn);
        trade.setOrderStatus(OrderStatusEnum.PAID.name());
        trade.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
        trade.setReceivableNo("-1");
        this.updateById(trade);

        orderService.payOrderZero(sn);
    }

    @Override
    public void updateTradePrice(String tradeSn) {
        baseMapper.updateTradePrice(tradeSn);
    }

    @Override
    public TradeDetailVO getTradeDetail(String orderSn) {

        SceneHelp.sceneHandler();

        TradeDetailVO tradeDetailVO = new TradeDetailVO();

        tradeDetailVO.setTrade(this.getBySn(orderService.getBySn(orderSn)
                .getTradeSn()));
        orderService.getByTradeSn(tradeDetailVO.getTrade()
                        .getSn())
                .forEach(order -> tradeDetailVO.getOrderDetailVOList()
                        .add(orderService.queryDetail(order.getSn())));

        return tradeDetailVO;
    }

    @Override
    @Transactional
    public void systemCancel(String sn, String reason) {

        Trade trade = this.getBySn(sn);
        if (trade == null) {
            return;
        }
        if (!trade.getOrderStatus().equals(OrderStatusEnum.UNPAID.name())) {
            throw new ServiceException(ResultCode.TRADE_STATUS_ERROR);
        }
        trade.setOrderStatus(OrderStatusEnum.CANCELLED.name());
        this.saveOrUpdate(trade);
        //取消订单
        orderService.getByTradeSn(sn).forEach(order -> {
                    if(order.getOrderStatus().equals(OrderStatusEnum.UNPAID.name())){
                        orderService.systemCancel(order.getSn(), reason, true);
                    }
                }
        );
    }

    /**
     * 交易信息 - 预处理
     * 如积分扣减、优惠券状态变更、设置砍价活动无效等
     *
     * @param tradeDTO 交易信息
     */
    private void pretreatment(TradeDTO tradeDTO) {

        //优惠券预处理
        couponPretreatment(tradeDTO);
        //积分预处理
        pointPretreatment(tradeDTO);
        //砍价订单预处理
        kanjiaPretreatment(tradeDTO);
    }

    /**
     * 优惠券预处理
     * 下单同时，扣除优惠券
     *
     * @param tradeDTO 购物车视图
     */
    private void couponPretreatment(TradeDTO tradeDTO) {
        List<MemberCouponDTO> memberCouponDTOList = new ArrayList<>();
        if (null != tradeDTO.getPlatformCoupon()) {
            memberCouponDTOList.add(tradeDTO.getPlatformCoupon());
        }
        List<TradeCouponDTO> storeCoupons = tradeDTO.getStoreCoupons();
        if (storeCoupons != null) {
            memberCouponDTOList.addAll(storeCoupons.stream().map(TradeCouponDTO::getMemberCouponDTO).toList());
        }
        if (memberCouponDTOList.isEmpty()) {
            return;
        }
        List<String> ids = memberCouponDTOList.stream().map(e -> e.getMemberCoupon().getId()).toList();
        memberCouponClient.used(tradeDTO.getMemberId(), ids);
        memberCouponDTOList.forEach(e -> promotionsClient.usedCoupon(e.getMemberCoupon().getCouponId(), 1));

    }

    /**
     * 创建交易，积分处理
     *
     * @param tradeDTO 购物车视图
     */
    private void pointPretreatment(TradeDTO tradeDTO) {

        //需要支付积分
        if (tradeDTO.getPriceDetailDTO() != null && tradeDTO.getPriceDetailDTO().getPayPoint() != null && tradeDTO.getPriceDetailDTO().getPayPoint() > 0) {
            StringBuilder orderSns = new StringBuilder();
            for (CartVO item : tradeDTO.getCartList()) {
                orderSns.append(item.getSn());
            }
            walletPointClient.updateMemberPoint(
                    UserPointUpdateDTO.builder()
                            .userId(tradeDTO.getMemberId())
                            .snReference(tradeDTO.getSn())
                            .description("订单【" + orderSns + "】创建，积分扣减" + tradeDTO.getPriceDetailDTO().getPayPoint())
                            .points(tradeDTO.getPriceDetailDTO().getPayPoint())
                            .userPointServiceEnum(UserPointServiceEnum.CONSUME)
                            .build());

        }
    }


    /**
     * 创建交易、砍价处理
     *
     * @param tradeDTO 购物车视图
     */
    private void kanjiaPretreatment(TradeDTO tradeDTO) {
        tradeDTO.getSkuList().forEach(item -> {
            if (item.getPromotionTypeEnum() == null) {
                return;
            }
            if (item.getPromotionTypeEnum().equals(PromotionTypeEnum.KANJIA)) {
                kanjiaActivityClient.endKanjiaActivity(item.getPromotionId());
            }
        });
    }

    /**
     * 创建订单最后一步校验
     *
     * @param tradeDTO 购物车视图
     */
    private void createTradeCheck(TradeDTO tradeDTO) {

        //创建订单如果没有收获地址，
        UserAddress userAddress = tradeDTO.getUserAddress();
        if (userAddress == null && !GoodsTypeEnum.VIRTUAL_GOODS.name().equals(tradeDTO.getCheckedSkuList().getFirst().getGoodsSku().getGoodsType())) {
            throw new ServiceException(ResultCode.MEMBER_ADDRESS_NOT_EXIST);
        }

        // 订单配送区域校验
        if (tradeDTO.getNotSupportFreight() != null && !tradeDTO.getNotSupportFreight().isEmpty()) {
            StringBuilder stringBuilder = new StringBuilder("包含商品有-");
            tradeDTO.getNotSupportFreight().forEach(sku -> stringBuilder.append(sku.getGoodsSku().getGoodsName()));
            throw new ServiceException(ResultCode.ORDER_NOT_SUPPORT_DISTRIBUTION, stringBuilder.toString());
        }

        if (tradeDTO.getCartList().stream().noneMatch(CartVO::getChecked)) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST_VALID);
        }
    }


}