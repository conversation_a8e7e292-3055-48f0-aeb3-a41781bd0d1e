package plus.qdt.modules.order.tpi.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import plus.qdt.modules.order.tpi.service.YzhOrderNoticeService;
import plus.qdt.modules.order.order.service.OrderService;
import plus.qdt.modules.order.order.service.OrderItemService;
import plus.qdt.modules.order.order.service.OrderPackageService;
import plus.qdt.modules.order.order.service.OrderPackageItemService;
import plus.qdt.modules.order.order.mapper.TpiOrderRecordMapper;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dos.OrderPackage;
import plus.qdt.modules.order.order.entity.dos.OrderPackageItem;
import plus.qdt.modules.order.order.entity.dos.TpiOrderRecord;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.Date;
import java.util.List;
import plus.qdt.modules.system.entity.dto.YzhOrderStatusNoticeRequestParam;
import plus.qdt.modules.system.entity.dto.YzhOrderStatusNoticeResponse;
import plus.qdt.modules.system.entity.dto.YzhLogisticsStatusNoticeRequestParam;
import plus.qdt.modules.system.entity.dto.YzhLogisticsStatusNoticeResponse;
import plus.qdt.modules.system.entity.dto.YzhOrderShipmentNoticeRequestParam;
import plus.qdt.modules.system.entity.dto.YzhOrderShipmentNoticeResponse;
import plus.qdt.modules.system.entity.enums.YzhOrderStatusEnum;
import plus.qdt.modules.system.entity.enums.YzhLogisticsStatusEnum;

/**
 * 云中鹤订单通知服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class YzhOrderNoticeServiceImpl implements YzhOrderNoticeService {

    private final OrderService orderService;
    private final OrderItemService orderItemService;
    private final OrderPackageService orderPackageService;
    private final OrderPackageItemService orderPackageItemService;
    private final TpiOrderRecordMapper tpiOrderRecordMapper;

    @Override
    public YzhOrderStatusNoticeResponse handleOrderStatusNotice(YzhOrderStatusNoticeRequestParam requestParam) {
        log.info("开始处理云中鹤订单状态通知，订单编号: {}, 父订单编号: {}, 客户方订单编号: {}, 订单状态: {}",
                requestParam.getOrderCode(), requestParam.getParentOrderCode(),
                requestParam.getTparOrderCode(), requestParam.getOrderStatus());

        try {
            // 1. 验证订单状态是否有效
            YzhOrderStatusEnum statusEnum = YzhOrderStatusEnum.getByCode(requestParam.getOrderStatus());
            if (statusEnum == null) {
                log.warn("无效的订单状态: {}", requestParam.getOrderStatus());
                return YzhOrderStatusNoticeResponse.error("10002", "无效的订单状态: " + requestParam.getOrderStatus());
            }

            // 2. 根据不同的订单状态执行不同的业务逻辑
            switch (statusEnum) {
                case CANCELLED:
                    return handleOrderCancelled(requestParam);
                case PENDING_SHIPMENT:
                    return handleOrderPendingShipment(requestParam);
                case SHIPPED:
                    return handleOrderShipped(requestParam);
                case COMPLETED:
                    return handleOrderCompleted(requestParam);
                default:
                    log.warn("未处理的订单状态: {}", statusEnum);
                    return YzhOrderStatusNoticeResponse.error("10003", "未处理的订单状态: " + statusEnum.getDescription());
            }

        } catch (Exception e) {
            log.error("处理订单状态通知异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhOrderStatusNoticeResponse.error("10001", "处理订单状态通知异常: " + e.getMessage());
        }
    }

    @Override
    public YzhOrderStatusNoticeResponse handleOrderCancelled(YzhOrderStatusNoticeRequestParam requestParam) {
        log.info("处理订单取消状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现订单取消的具体业务逻辑
            // 1. 更新本地订单状态为已取消
            // 2. 处理库存回滚
            // 3. 处理退款逻辑
            // 4. 发送取消通知给用户
            // 5. 记录订单状态变更日志

            log.info("订单取消处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhOrderStatusNoticeResponse.success("订单取消状态处理成功");

        } catch (Exception e) {
            log.error("处理订单取消状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhOrderStatusNoticeResponse.error("20001", "处理订单取消状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhOrderStatusNoticeResponse handleOrderPendingShipment(YzhOrderStatusNoticeRequestParam requestParam) {
        log.info("处理订单待发货状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现订单待发货的具体业务逻辑
            // 1. 更新本地订单状态为待发货
            // 2. 通知仓库准备发货
            // 3. 发送待发货通知给用户
            // 4. 记录订单状态变更日志

            log.info("订单待发货处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhOrderStatusNoticeResponse.success("订单待发货状态处理成功");

        } catch (Exception e) {
            log.error("处理订单待发货状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhOrderStatusNoticeResponse.error("20002", "处理订单待发货状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhOrderStatusNoticeResponse handleOrderShipped(YzhOrderStatusNoticeRequestParam requestParam) {
        log.info("处理订单已发货状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现订单已发货的具体业务逻辑
            // 1. 更新本地订单状态为已发货
            // 2. 获取物流信息并更新
            // 3. 发送发货通知给用户（包含物流信息）
            // 4. 启动物流跟踪
            // 5. 记录订单状态变更日志

            log.info("订单已发货处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhOrderStatusNoticeResponse.success("订单已发货状态处理成功");

        } catch (Exception e) {
            log.error("处理订单已发货状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhOrderStatusNoticeResponse.error("20003", "处理订单已发货状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhOrderStatusNoticeResponse handleOrderCompleted(YzhOrderStatusNoticeRequestParam requestParam) {
        log.info("处理订单已完成状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现订单已完成的具体业务逻辑
            // 1. 更新本地订单状态为已完成
            // 2. 处理订单结算
            // 3. 发送完成通知给用户
            // 4. 触发评价提醒
            // 5. 处理积分奖励
            // 6. 记录订单状态变更日志

            log.info("订单已完成处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhOrderStatusNoticeResponse.success("订单已完成状态处理成功");

        } catch (Exception e) {
            log.error("处理订单已完成状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhOrderStatusNoticeResponse.error("20004", "处理订单已完成状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhLogisticsStatusNoticeResponse handleLogisticsStatusNotice(YzhLogisticsStatusNoticeRequestParam requestParam) {
        log.info("开始处理云中鹤物流状态通知，订单编号: {}, 父订单编号: {}, 客户方订单编号: {}, 物流状态: {}",
                requestParam.getOrderCode(), requestParam.getParentOrderCode(),
                requestParam.getTparOrderCode(), requestParam.getLogisticsStatus());

        try {
            // 1. 验证物流状态是否有效
            YzhLogisticsStatusEnum statusEnum = YzhLogisticsStatusEnum.getByCode(requestParam.getLogisticsStatus());
            if (statusEnum == null) {
                log.warn("无效的物流状态: {}", requestParam.getLogisticsStatus());
                return YzhLogisticsStatusNoticeResponse.error("10002", "无效的物流状态: " + requestParam.getLogisticsStatus());
            }

            // 2. 根据不同的物流状态执行不同的业务逻辑
            switch (statusEnum) {
                case PENDING_PICKUP:
                    return handlePendingPickup(requestParam);
                case PICKED_UP:
                    return handlePickedUp(requestParam);
                case IN_TRANSIT:
                    return handleInTransit(requestParam);
                case DELIVERED:
                    return handleDelivered(requestParam);
                case REJECTED:
                    return handleRejected(requestParam);
                default:
                    log.warn("未处理的物流状态: {}", statusEnum);
                    return YzhLogisticsStatusNoticeResponse.error("10003", "未处理的物流状态: " + statusEnum.getDescription());
            }

        } catch (Exception e) {
            log.error("处理物流状态通知异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhLogisticsStatusNoticeResponse.error("10001", "处理物流状态通知异常: " + e.getMessage());
        }
    }
    @Override
    public YzhLogisticsStatusNoticeResponse handlePendingPickup(YzhLogisticsStatusNoticeRequestParam requestParam) {
        log.info("处理待揽件状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现待揽件的具体业务逻辑
            // 1. 更新本地物流状态为待揽件
            // 2. 发送待揽件通知给用户
            // 3. 记录物流状态变更日志
            // 4. 通知相关系统

            log.info("待揽件处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhLogisticsStatusNoticeResponse.success("待揽件状态处理成功");

        } catch (Exception e) {
            log.error("处理待揽件状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhLogisticsStatusNoticeResponse.error("30001", "处理待揽件状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhLogisticsStatusNoticeResponse handlePickedUp(YzhLogisticsStatusNoticeRequestParam requestParam) {
        log.info("处理已揽件状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现已揽件的具体业务逻辑
            // 1. 更新本地物流状态为已揽件
            // 2. 发送已揽件通知给用户
            // 3. 记录物流状态变更日志
            // 4. 启动物流跟踪

            log.info("已揽件处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhLogisticsStatusNoticeResponse.success("已揽件状态处理成功");

        } catch (Exception e) {
            log.error("处理已揽件状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhLogisticsStatusNoticeResponse.error("30002", "处理已揽件状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhLogisticsStatusNoticeResponse handleInTransit(YzhLogisticsStatusNoticeRequestParam requestParam) {
        log.info("处理运输中状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现运输中的具体业务逻辑
            // 1. 更新本地物流状态为运输中
            // 2. 发送运输中通知给用户
            // 3. 记录物流状态变更日志
            // 4. 更新物流跟踪信息

            log.info("运输中处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhLogisticsStatusNoticeResponse.success("运输中状态处理成功");

        } catch (Exception e) {
            log.error("处理运输中状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhLogisticsStatusNoticeResponse.error("30003", "处理运输中状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhLogisticsStatusNoticeResponse handleDelivered(YzhLogisticsStatusNoticeRequestParam requestParam) {
        log.info("处理已签收状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现已签收的具体业务逻辑
            // 1. 更新本地物流状态为已签收
            // 2. 发送签收通知给用户
            // 3. 记录物流状态变更日志
            // 4. 触发订单完成流程
            // 5. 处理积分奖励

            log.info("已签收处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhLogisticsStatusNoticeResponse.success("已签收状态处理成功");

        } catch (Exception e) {
            log.error("处理已签收状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhLogisticsStatusNoticeResponse.error("30004", "处理已签收状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhLogisticsStatusNoticeResponse handleRejected(YzhLogisticsStatusNoticeRequestParam requestParam) {
        log.info("处理拒收状态，订单编号: {}", requestParam.getOrderCode());

        try {
            // TODO: 在这里实现拒收的具体业务逻辑
            // 1. 更新本地物流状态为拒收
            // 2. 发送拒收通知给用户
            // 3. 记录物流状态变更日志
            // 4. 启动退货流程
            // 5. 处理退款逻辑

            log.info("拒收处理完成，订单编号: {}", requestParam.getOrderCode());
            return YzhLogisticsStatusNoticeResponse.success("拒收状态处理成功");

        } catch (Exception e) {
            log.error("处理拒收状态异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhLogisticsStatusNoticeResponse.error("30005", "处理拒收状态失败: " + e.getMessage());
        }
    }

    @Override
    public YzhOrderShipmentNoticeResponse handleOrderShipmentNotice(YzhOrderShipmentNoticeRequestParam requestParam) {
        log.info("开始处理云中鹤订单发货通知，订单编号: {}, 父订单编号: {}, 客户方订单编号: {}, 快递公司: {}, 快递单号: {}",
                requestParam.getOrderCode(), requestParam.getParentOrderCode(),
                requestParam.getTparOrderCode(), requestParam.getExpressName(), requestParam.getExpressNo());

        try {
            // 1. 验证快递信息
            if (requestParam.getExpressCode() == null || requestParam.getExpressCode().trim().isEmpty()) {
                log.warn("快递公司编码为空，订单编号: {}", requestParam.getOrderCode());
                return YzhOrderShipmentNoticeResponse.error("40001", "快递公司编码不能为空");
            }

            if (requestParam.getExpressName() == null || requestParam.getExpressName().trim().isEmpty()) {
                log.warn("快递公司名称为空，订单编号: {}", requestParam.getOrderCode());
                return YzhOrderShipmentNoticeResponse.error("40002", "快递公司名称不能为空");
            }

            if (requestParam.getExpressNo() == null || requestParam.getExpressNo().trim().isEmpty()) {
                log.warn("快递单号为空，订单编号: {}", requestParam.getOrderCode());
                return YzhOrderShipmentNoticeResponse.error("40003", "快递单号不能为空");
            }

            // 2. 处理订单发货业务逻辑
            processOrderShipment(requestParam);

            log.info("订单发货通知处理完成，订单编号: {}, 快递公司: {}, 快递单号: {}",
                    requestParam.getOrderCode(), requestParam.getExpressName(), requestParam.getExpressNo());
            return YzhOrderShipmentNoticeResponse.success("订单发货通知处理成功");

        } catch (Exception e) {
            log.error("处理订单发货通知异常，订单编号: {}, 错误信息: {}", requestParam.getOrderCode(), e.getMessage(), e);
            return YzhOrderShipmentNoticeResponse.error("40000", "处理订单发货通知失败: " + e.getMessage());
        }
    }

    /**
     * 处理订单发货业务逻辑
     *
     * @param requestParam 发货通知请求参数
     */
    private void processOrderShipment(YzhOrderShipmentNoticeRequestParam requestParam) {
        // 1. 根据云中鹤订单编号查找对应的OrderItem
        List<OrderItem> orderItems = findOrderItemsByYzhOrderCode(requestParam.getOrderCode());
        if (orderItems.isEmpty()) {
            throw new RuntimeException("未找到对应的订单商品，云中鹤订单编号: " + requestParam.getOrderCode());
        }

        // 2. 通过OrderItem获取订单信息
        String systemOrderSn = orderItems.get(0).getOrderSn();
        Order order = orderService.getBySn(systemOrderSn);
        if (order == null) {
            throw new RuntimeException("订单不存在，订单编号: " + systemOrderSn);
        }

        // 3. 创建包裹信息
        OrderPackage orderPackage = createOrderPackage(order, requestParam);
        orderPackageService.save(orderPackage);

        // 4. 创建包裹明细
        createOrderPackageItems(orderPackage, orderItems);

        // 5. 更新OrderItem的发货状态
        updateOrderItemsDeliveryStatus(orderItems);

        log.info("订单发货处理完成，系统订单号: {}, 云中鹤订单编号: {}, 包裹号: {}",
                systemOrderSn, requestParam.getOrderCode(), orderPackage.getPackageNo());
    }

    /**
     * 根据云中鹤订单编号查找系统订单号
     *
     * @param yzhOrderCode 云中鹤订单编号
     * @return 系统订单号
     */
    private String findSystemOrderSn(String yzhOrderCode) {
        // 方案1：通过TpiOrderRecord查找（如果云中鹤订单编号存储在thirdPartyOrderId字段中）
        LambdaQueryWrapper<TpiOrderRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TpiOrderRecord::getThirdPartyOrderId, yzhOrderCode)
                .eq(TpiOrderRecord::getSupplierType, SupplierEnum.YZH.name())
                .orderByDesc(TpiOrderRecord::getCreateTime)
                .last("LIMIT 1");

        TpiOrderRecord tpiOrderRecord = tpiOrderRecordMapper.selectOne(queryWrapper);
        if (tpiOrderRecord != null) {
            return tpiOrderRecord.getSystemOrderSn();
        }

        // 方案2：如果方案1找不到，尝试通过父订单编号查找
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TpiOrderRecord::getThirdPartyParentOrderCode, yzhOrderCode)
                .eq(TpiOrderRecord::getSupplierType, SupplierEnum.YZH.name())
                .orderByDesc(TpiOrderRecord::getCreateTime)
                .last("LIMIT 1");

        tpiOrderRecord = tpiOrderRecordMapper.selectOne(queryWrapper);
        if (tpiOrderRecord != null) {
            return tpiOrderRecord.getSystemOrderSn();
        }

        log.warn("未找到对应的系统订单，云中鹤订单编号: {}", yzhOrderCode);
        return null;
    }

    /**
     * 根据云中鹤订单编号查找对应的OrderItem
     *
     * @param yzhOrderCode 云中鹤订单编号
     * @return 订单商品列表
     */
    private List<OrderItem> findOrderItemsByYzhOrderCode(String yzhOrderCode) {
        // 1. 先通过TpiOrderRecord查找系统订单号
        String systemOrderSn = findSystemOrderSn(yzhOrderCode);
        if (systemOrderSn == null) {
            throw new RuntimeException("未找到对应的本地订单，云中鹤订单编号: " + yzhOrderCode);
        }

        // 2. 获取该订单下的所有OrderItem
        // 注意：这里简化处理，将整个订单的所有商品都作为一个包裹
        // 如果需要更精确的匹配，可能需要在OrderItem中增加字段来关联云中鹤子订单编号
        return orderItemService.getByOrderSn(systemOrderSn);
    }

    /**
     * 创建订单包裹
     *
     * @param order 订单信息
     * @param requestParam 发货通知请求参数
     * @return 订单包裹
     */
    private OrderPackage createOrderPackage(Order order, YzhOrderShipmentNoticeRequestParam requestParam) {
        OrderPackage orderPackage = new OrderPackage();
        orderPackage.setPackageNo(SnowFlake.createStr("OP"));
        orderPackage.setOrderSn(order.getSn());
        orderPackage.setLogisticsNo(requestParam.getExpressNo());
        orderPackage.setLogisticsCode(requestParam.getExpressCode());
        orderPackage.setLogisticsName(requestParam.getExpressName());
        orderPackage.setStatus("1"); // 1表示已发货
        orderPackage.setConsigneeMobile(order.getConsigneeMobile());
        return orderPackage;
    }

    /**
     * 创建包裹明细
     *
     * @param orderPackage 订单包裹
     * @param orderItems 订单商品列表
     */
    private void createOrderPackageItems(OrderPackage orderPackage, List<OrderItem> orderItems) {
        for (OrderItem orderItem : orderItems) {
            OrderPackageItem packageItem = new OrderPackageItem();
            packageItem.setPackageNo(orderPackage.getPackageNo());
            packageItem.setOrderSn(orderPackage.getOrderSn());
            packageItem.setOrderItemSn(orderItem.getSn());
            packageItem.setGoodsName(orderItem.getGoodsName());
            packageItem.setThumbnail(orderItem.getImage());
            packageItem.setDeliverNumber(orderItem.getNum()); // 全部发货
            packageItem.setLogisticsTime(new Date());

            orderPackageItemService.save(packageItem);
        }
    }

    /**
     * 更新OrderItem的发货状态
     *
     * @param orderItems 订单商品列表
     */
    private void updateOrderItemsDeliveryStatus(List<OrderItem> orderItems) {
        for (OrderItem orderItem : orderItems) {
            // 更新已发货数量为总数量（表示全部发货）
            orderItem.setDeliverNumber(orderItem.getNum());
            orderItemService.updateById(orderItem);
        }
    }
}
