package plus.qdt.modules.order.order.task.enums;

public enum ApiUrlEnum {

    CREATE_ORDER("/open/xdxt/api/v2/order/createOrder", "创建订单"),

    QUERY_ORDER_CHANGE_EVENT("/open/xdxt/api/v2/order/listOrderChangeEvent", "查询订单变更通知");

    private final String url;
    private final String desc;

    private ApiUrlEnum(String url, String desc) {
        this.url = url;
        this.desc = desc;
    }

    public String getUrl() {
        return this.url;
    }

    public String getDesc() {
        return this.desc;
    }
}
