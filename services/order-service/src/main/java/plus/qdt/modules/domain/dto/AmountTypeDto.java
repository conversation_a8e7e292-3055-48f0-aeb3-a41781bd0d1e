package plus.qdt.modules.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import plus.qdt.modules.jinTongAccount.enums.CaiTongAccountLogEnum;
import plus.qdt.modules.jinTongAccount.enums.JinTongAccountLogEnums;
import plus.qdt.modules.jinTongAccount.enums.StoreAmountLogEnum;
import plus.qdt.modules.jinTongAccount.enums.UserAmountLogEnum;

/**
 * <AUTHOR>
 * @since 2.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmountTypeDto {

    /**
     * 平台金通
     */
    private JinTongAccountLogEnums jinTong;

    /**
     * 用户财通消费类型
     */
    private UserAmountLogEnum user;

    /**
     * 商家财通消费类型
     */
    private StoreAmountLogEnum store;

    /**
     * 平台财通钱包类型
     */
    private CaiTongAccountLogEnum caiTong;

    /**
     * 是否为虚拟商品
     */
    private boolean virtual;

    public AmountTypeDto(JinTongAccountLogEnums jinTong, UserAmountLogEnum user, CaiTongAccountLogEnum caiTong) {
        this.jinTong = jinTong;
        this.user = user;
        this.caiTong = caiTong;
    }
}
