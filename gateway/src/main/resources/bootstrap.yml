#dataId 的拼接格式如下
#    ${prefix} - ${spring.profiles.active} . ${file-extension}
#    prefix 默认为 spring.application.name 的值，也可以通过配置项 spring.cloud.nacos.config.prefix来配置。
#    spring.profiles.active 即为当前环境对应的 profile，详情可以参考 Spring Boot文档
#    注意，当 activeprofile 为空时，对应的连接符 - 也将不存在，dataId 的拼接格式变成 ${prefix}.${file-extension}
#    file-extension 为配置内容的数据格式，可以通过配置项 spring.cloud.nacos.config.file-extension来配置。 目前只支持 properties 类型。
#    group 默认为 DEFAULT_GROUP，可以通过 spring.cloud.nacos.config.group 配置。
server:
  port: 8888

spring:
  main:
    web-application-type: reactive
  application:
    name: '@artifactId@'
  cloud:
    nacos:
      discovery:
        server-addr: '@nacos.server@'
#        namespace: '@nacos.namespaceId@'
        namespace: 9b30b4b2-b156-49b8-97ae-2f38725322b9
        group: '@nacos.group@'
        username: '@nacos.username@'
        password: '@nacos.password@'
        metadata:
          version: wnm
      config:
        server-addr: '@nacos.server@'
        username: '@nacos.username@'
        password: '@nacos.password@'
        namespace: '@nacos.namespaceId@'
        group: '@nacos.group@'
        file-extension: yml
        shared-configs:
          - data-id: <EMAIL>@.${spring.cloud.nacos.config.file-extension} # 配置文件名-Data Id
            group: @nacos.group@  # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false

  profiles:
    active: '@profiles.active@'
