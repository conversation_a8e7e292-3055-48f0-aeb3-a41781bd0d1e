package plus.qdt.modules.order.aftersale.entity.dto;


import plus.qdt.modules.promotion.entity.dos.BaseStandardPromotions;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 商城退款流水
 *
 * <AUTHOR>
 * @since 2020/11/17 7:25 下午
 */
@Data
public class AfterSalePriceDetailDTO implements Serializable {


    private static final long serialVersionUID = 8808470688518188146L;
    @Schema(title = "商品总金额（商品原价）")
    private Double goodsPrice;

    @Schema(title = "配送费")
    private Double freightPrice;

    //============discount price============

    @Schema(title = "支付积分")
    private Integer payPoint;

    @Schema(title = "优惠金额")
    private Double discountPrice;

    @Schema(title = "优惠券金额")
    private Double couponPrice;

    //===========end discount price =============


    //=========distribution==========

    @Schema(title = "单品分销返现支出")
    private Double distributionCommission;


    @Schema(title = "平台收取交易佣金")
    private Double platFormCommission;

    //=========end distribution==========


    //========= platform coupon==========

    @Schema(title = "平台优惠券 使用金额")
    private Double siteCouponPrice;

    @Schema(title = "站点优惠券佣金比例")
    private Double siteCouponPoint;

    @Schema(title = "站点优惠券佣金")
    private Double siteCouponCommission;
    //=========end platform coupon==========

    @Schema(title = "流水金额(入账 出帐金额) = goodsPrice - discountPrice - couponPrice")
    private Double flowPrice;

    @Schema(title = "最终结算金额 = flowPrice - platFormCommission - distributionCommission")
    private Double billPrice;

    /**
     * 参与的促销活动
     */
    @Schema(title = "参与的促销活动")
    private List<BaseStandardPromotions> joinPromotion;


    public AfterSalePriceDetailDTO() {
        goodsPrice = 0d;
        freightPrice = 0d;

        payPoint = 0;
        discountPrice = 0d;

        distributionCommission = 0d;
        platFormCommission = 0d;

        siteCouponPrice = 0d;
        siteCouponPoint = 0d;
        siteCouponCommission = 0d;

        flowPrice = 0d;
        billPrice = 0d;

        joinPromotion = new ArrayList<>();
    }

}
