package plus.qdt.modules.goods.entity.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品参数项
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-06-24 15:41
 */
@Data
@Schema(title = "商品参数列表")
public class GoodsParamsItemDTO implements Serializable {

    private static final long serialVersionUID = -8823775607604091035L;

    @Schema(title = "参数ID")
    private String paramId;

    @Schema(title = "参数名字")
    private String paramName;

    @Schema(title = "参数值")
    private String paramValue;

    @Schema(title = "是否可索引，0 不索引 1 索引")
    private Integer isIndex = 0;

    @Schema(title = "是否必填，0 不显示 1 显示")
    private Integer required = 0;

    @Schema(title = "排序")
    private Integer sort;
}
