package plus.qdt.modules.goods.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 小程序直播商品
 *
 * <AUTHOR>
 * @since 2021/5/17 9:34 上午
 */
@Data
@Schema(title = "Commodity", description = "直播商品")
@TableName("li_commodity")
@EqualsAndHashCode(callSuper = true)
public class Commodity extends BaseStandardEntity {

    private static final long serialVersionUID = 6613989719119657196L;

    @Schema(title = "图片")
    private String goodsImage;

    @Schema(title = "商品名称")
    private String name;

    /**
     * 1：一口价（只需要传入price，price2不传）
     * 2：价格区间（price字段为左边界，price2字段为右边界，price和price2必传）
     * 3：显示折扣价（price字段为原价，price2字段为现价， price和price2必传
     */
    @Schema(title = "价格类型")
    private Integer priceType;

    @Schema(title = "价格")
    private Double price;

    @Schema(title = "价格2")
    private Double price2;

    @Schema(title = "商品详情页的小程序路径")
    private String url;

    @Schema(title = "微信程序直播商品ID")
    private Integer liveGoodsId;

    @Schema(title = "审核单ID")
    private String auditId;

    @Schema(title = "审核状态")
    private String auditStatus;

    @Schema(title = "店铺ID")
    private String storeId;

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "规格ID")
    private String skuId;

}
