package plus.qdt.modules.promotion.entity.dto;


import plus.qdt.modules.promotion.entity.dos.KanjiaActivityLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 砍价活动参与实体类
 *
 * <AUTHOR>
 * @date 2020-7-1 10:44 上午
 */
@Data
@Schema(title = "砍价活动参与记录对象")
public class KanjiaActivityLogDTO extends KanjiaActivityLog {

    @Schema(title = "砍价商品Id")
    private String kanjiaActivityGoodsId;

}