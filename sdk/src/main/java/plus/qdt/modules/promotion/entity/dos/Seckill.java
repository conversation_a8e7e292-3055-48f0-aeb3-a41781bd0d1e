package plus.qdt.modules.promotion.entity.dos;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.utils.RegularUtil;
import plus.qdt.common.utils.ValidateParamsUtil;
import plus.qdt.modules.promotion.entity.vos.SeckillVO;
import plus.qdt.modules.promotion.tools.PromotionTools;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serial;
import java.util.Date;

/**
 * 秒杀活动实体类
 *
 * <AUTHOR>
 * @since 2020-03-19 10:44 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_seckill")
@Schema(title = "秒杀活动活动")
@NoArgsConstructor
public class Seckill extends BaseStandardPromotions {

    @Serial
    private static final long serialVersionUID = -9116425737163730836L;

    @NotNull(message = "请填写报名截止时间")
    @Schema(title = "报名截至时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date, pattern = "yyyy-MM-dd HH:mm:ss || yyyy-MM-dd || yyyy/MM/dd HH:mm:ss|| yyyy/MM/dd ||epoch_millis")
    private Date applyEndTime;

    @Schema(title = "申请规则")
    private String seckillRule;

    @Schema(title = "开启几点场 例如：6，8，12")
    @NotNull(message = "活动时间段不能为空")
    private String hours;

    /**
     * 已参与此活动的商家id集合
     */
    @Schema(title = "商家id集合以逗号分隔")
    private String storeIds;

    @Schema(title = "商品数量")
    private Integer goodsNum;

    public Seckill(int day, String hours, String seckillRule) {
        //默认创建*天后的秒杀活动
        DateTime dateTime = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), day));
        this.applyEndTime = dateTime;
        this.hours = hours;
        this.seckillRule = seckillRule;
        this.goodsNum = 0;
        //BasePromotion
        this.setStoreName(PromotionTools.PLATFORM_NAME);
        this.setStoreId(PromotionTools.PLATFORM_ID);
        this.setPromotionName(DateUtil.formatDate(dateTime) + " 秒杀活动");
        this.setApplyEndTime(dateTime);
        this.setStartTime(dateTime);
        this.setEndTime(DateUtil.endOfDay(dateTime));
    }

    public Seckill(SeckillVO seckillVO) {
        BeanUtils.copyProperties(seckillVO, this);
    }

    public boolean validateParams() {
        if (applyEndTime == null) {
            ValidateParamsUtil.throwInvalidParamError("报名截止时间不正确");
        }
        if (CharSequenceUtil.isBlank(hours) || !RegularUtil.seckillTime(hours)) {
            ValidateParamsUtil.throwInvalidParamError("活动时间段不正确");
        }
        if (!ValidateParamsUtil.isValidString(seckillRule, 2, 200, false)) {
            ValidateParamsUtil.throwInvalidParamError("活动规则长度限制2-200个字符");
        }
        if (!ValidateParamsUtil.isValidIntValue(goodsNum, 0, true)) {
            ValidateParamsUtil.throwInvalidParamError("商品数量不正确");
        }

        return super.validateParams();
    }

}