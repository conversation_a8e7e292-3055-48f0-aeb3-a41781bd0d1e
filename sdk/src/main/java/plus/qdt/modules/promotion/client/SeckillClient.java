package plus.qdt.modules.promotion.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.promotion.entity.dos.Seckill;
import plus.qdt.modules.promotion.entity.dto.SeckillCheckDTO;
import plus.qdt.modules.promotion.entity.dto.search.SeckillSearchParams;
import plus.qdt.modules.promotion.fallback.SeckillFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/14
 **/
@FeignClient(name = ServiceConstant.PROMOTION_SERVICE, contextId = "seckill",fallback = SeckillFallback.class)
public interface SeckillClient {

    /**
     * 从系统设置中获取秒杀活动的配置
     * 添加30天后的秒杀活动
     * @return 是否成功
     */
    @PostMapping("/feign/promotion/seckill/addSeckill")
    boolean generatorSeckill();

    @PostMapping("/feign/promotion/seckill")
    boolean savePromotions(@RequestBody Seckill seckill);

    /**
     * 根据条件查询秒杀活动
     *
     * @param searchParams 查询参数
     * @return 秒杀活动列表
     */
    @PostMapping("/feign/promotion/seckill/list")
    List<Seckill> listFindAll(@RequestBody SeckillSearchParams searchParams);


    /**
     * 检测秒杀活动是否存在
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param activityId 活动id
     * @return 秒杀活动列表
     */
    @PostMapping("/feign/promotion/seckill/check")
    List<Seckill> checkSeckillIsActive(@RequestBody SeckillCheckDTO seckillCheckDTO);
}
