package plus.qdt.modules.distribution.entity.dos;

import plus.qdt.common.utils.BeanUtil;
import plus.qdt.modules.distribution.entity.dto.DistributionApplyDTO;
import plus.qdt.modules.distribution.entity.enums.DistributionStatusEnum;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 分销员对象
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@Data
@Schema(title = "分销员")
@TableName("li_distribution")
@NoArgsConstructor
public class Distribution extends BaseStandardEntity {


    private static final long serialVersionUID = -4878132663540847325L;

    public Distribution(String memberId, String memberName, DistributionApplyDTO distributionApplyDTO) {
        this.memberId = memberId;
        this.memberName = memberName;
        this.distributionOrderCount = 0;
        this.rebateTotal = 0D;
        this.canRebate = 0D;
        this.commissionFrozen = 0D;
        this.distributionStatus = DistributionStatusEnum.APPLY.name();
        this.peopleNum = 0;
        BeanUtil.copyProperties(distributionApplyDTO, this);
    }
    public Distribution(String memberId, String memberName) {
        this.memberId = memberId;
        this.memberName = memberName;
        this.distributionOrderCount = 0;
        this.rebateTotal = 0D;
        this.distributionStatus = DistributionStatusEnum.PASS.name();
        this.peopleNum = 0;
    }

    @Schema(title = "会员id")
    private String memberId;

    @Schema(title = "会员名称")
    private String memberName;

    @Schema(title = "会员姓名")
    private String name;

    @Schema(title = "身份证号")
    private String idNumber;

    @Schema(title = "分润总额")
    private Double rebateTotal = 0D;

    @Schema(title = "可提现金额")
    private Double canRebate = 0D;

    @Schema(title = "冻结金额")
    private Double commissionFrozen = 0D;

    @Schema(title = "分销订单数")
    private Integer distributionOrderCount;

    /**
     * @see DistributionStatusEnum
     */
    @Schema(title = "分销员状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String distributionStatus;

    @Length(min = 1, max = 200, message = "结算银行开户行名称长度为1-200位")
    @NotBlank(message = "结算银行开户行名称不能为空")
    @Schema(title = "结算银行开户行名称")
    private String settlementBankAccountName;

    @Length(min = 1, max = 200, message = "结算银行开户账号长度为1-200位")
    @NotBlank(message = "结算银行开户账号不能为空")
    @Schema(title = "结算银行开户账号")
    private String settlementBankAccountNum;

    @Length(min = 1, max = 200, message = "结算银行开户支行名称长度为1-200位")
    @NotBlank(message = "结算银行开户支行名称不能为空")
    @Schema(title = "结算银行开户支行名称")
    private String settlementBankBranchName;

    @Schema(title = "推广人数")
    private Integer peopleNum;

    @Schema(title = "分销订单金额")
    private Double distributionOrderPrice;
}