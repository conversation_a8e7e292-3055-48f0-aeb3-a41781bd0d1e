package plus.qdt.modules.store.entity.dto;

import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.statistics.entity.dto.StatisticsQueryParam;
import plus.qdt.modules.statistics.entity.vo.OrderOverviewVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/6/24
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class StoreStatisticsOverviewSearchParams extends PageVO {

    @Serial
    private static final long serialVersionUID = 6573522064030370929L;

    private Date[] dates;

    private StatisticsQueryParam statisticsQueryParam;


}
