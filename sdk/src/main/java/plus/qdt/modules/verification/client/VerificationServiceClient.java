package plus.qdt.modules.verification.client;

import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.verification.entity.enums.VerificationEnums;
import plus.qdt.modules.verification.fallback.VerificationServiceFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 验证码 client
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-08 09:52
 */
@FeignClient(name = ServiceConstant.AUTH_SERVICE, contextId = "verification", fallback = VerificationServiceFallback.class)
public interface VerificationServiceClient {

    /**
     * 校验验证码
     *
     * @param uuid              uuid
     * @param verificationEnums 验证枚举
     * @return 校验结果
     */
    @PostMapping("/feign/verification/{uuid}")
    boolean check(@PathVariable String uuid, @RequestParam SceneEnums sceneEnums, @RequestParam VerificationEnums verificationEnums);
}
