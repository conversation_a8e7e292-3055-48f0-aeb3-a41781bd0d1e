package plus.qdt.modules.system.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.search.entity.dos.HotWordsHistory;
import plus.qdt.modules.system.client.HotWordsHistoryClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/1
 **/
public class HotWordsHistoryFallback implements HotWordsHistoryClient {

    @Override
    public boolean saveBatch(List<HotWordsHistory> hotWordsHistories) {
        throw new ServiceException();
    }
}
