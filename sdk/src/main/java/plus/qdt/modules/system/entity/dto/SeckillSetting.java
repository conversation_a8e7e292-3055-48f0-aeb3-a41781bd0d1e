package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 秒杀活动设置
 *
 * <AUTHOR>
 * @since 2021/5/24 3:27 下午
 */
@Data
public class SeckillSetting implements Serializable {

    @Schema(title = "开启几点场 例如：6,8,12")
    @NotNull(message = "活动时间段不能为空")
    private String hours;


    @Schema(title = "是否开启审核")
    private Boolean needCheck = false;

    @Schema(title = "秒杀规则")
    @NotNull(message = "秒杀规则不能为空")
    private String seckillRule;

    @NotNull(message = "自动创建周期")
    private Integer creation;

    public Boolean getNeedCheck() {
        if (needCheck == null) {
            return false;
        }
        return needCheck;
    }

    public Integer getCreation() {
        if (creation == null) {
            return 7;
        }
        return creation;
    }
}
