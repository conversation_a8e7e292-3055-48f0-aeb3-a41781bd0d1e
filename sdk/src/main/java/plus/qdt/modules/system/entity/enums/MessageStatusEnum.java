package plus.qdt.modules.system.entity.enums;

/**
 * <AUTHOR>
 * @since 2025-07-02
 */
public enum MessageStatusEnum {
    PENDING(0, "待发送"),

    SENT(1, "已发送"),

    FAILED(2, "发送失败");

    private final int code;
    private final String description;

    MessageStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取对应的枚举值
     * @param code 状态码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果传入无效的状态码
     */
    public static MessageStatusEnum fromCode(int code) {
        for (MessageStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的消息状态码: " + code);
    }

    /**
     * 根据状态码获取状态描述
     * @param code 状态码
     * @return 状态描述文本
     */
    public static String getDescription(int code) {
        return fromCode(code).getDescription();
    }

    /**
     * 检查给定的状态码是否有效
     * @param code 待检查的状态码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        for (MessageStatusEnum status : values()) {
            if (status.code == code) {
                return true;
            }
        }
        return false;
    }
}
