package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 云中鹤通知响应结果基类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Schema(description = "云中鹤通知响应结果基类")
public class YzhNoticeBaseResponse {

    @Schema(description = "接口成功与否标识")
    private Boolean success;

    @Schema(description = "接口业务编码")
    private String code;

    @Schema(description = "接口业务描述")
    private String desc;

    @Schema(description = "接口业务数据对象")
    private Object result;

    @Schema(description = "接口响应的时间戳")
    private Long time;

    /**
     * 创建成功响应的通用方法
     * 由于SuperBuilder的限制，这里提供基础实现，子类可以重写
     *
     * @param message 成功消息
     * @return 成功响应
     */
    protected static YzhNoticeBaseResponse createSuccessBase(String message) {
        return YzhNoticeBaseResponse.builder()
                .success(true)
                .code("00000")
                .desc(message)
                .result(null)
                .time(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建失败响应的通用方法
     * 由于SuperBuilder的限制，这里提供基础实现，子类可以重写
     *
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     * @return 失败响应
     */
    protected static YzhNoticeBaseResponse createErrorBase(String errorCode, String errorMessage) {
        return YzhNoticeBaseResponse.builder()
                .success(false)
                .code(errorCode != null ? errorCode : "10001")
                .desc(errorMessage)
                .result(null)
                .time(System.currentTimeMillis())
                .build();
    }
}
