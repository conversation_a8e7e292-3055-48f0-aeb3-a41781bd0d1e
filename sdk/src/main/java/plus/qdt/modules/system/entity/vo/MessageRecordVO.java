package plus.qdt.modules.system.entity.vo;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import plus.qdt.modules.system.entity.MessageRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 消息记录查询条件
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@Schema(title = "消息记录查询条件")
public class MessageRecordVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "消息唯一ID")
    private String messageId;

    @Schema(title = "业务类型")
    private String bizType;

    @Schema(title = "业务唯一键")
    private String bizKey;

    @Schema(title = "消息状态", description = "0-待发送 1-已发送 2-发送失败")
    private Integer messageStatus;

    @Schema(title = "目标队列")
    private String targetQueue;

    @Schema(title = "创建时间开始")
    private Date createdAtStart;

    @Schema(title = "创建时间结束")
    private Date createdAtEnd;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<MessageRecord> buildQueryWrapper() {
        LambdaQueryWrapper<MessageRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        if (CharSequenceUtil.isNotEmpty(messageId)) {
            queryWrapper.like(MessageRecord::getMessageId, messageId);
        }
        
        if (CharSequenceUtil.isNotEmpty(bizType)) {
            queryWrapper.eq(MessageRecord::getBizType, bizType);
        }
        
        if (CharSequenceUtil.isNotEmpty(bizKey)) {
            queryWrapper.like(MessageRecord::getBizKey, bizKey);
        }
        
        if (messageStatus != null) {
            queryWrapper.eq(MessageRecord::getMessageStatus, messageStatus);
        }
        
        if (CharSequenceUtil.isNotEmpty(targetQueue)) {
            queryWrapper.eq(MessageRecord::getTargetQueue, targetQueue);
        }
        
        if (createdAtStart != null) {
            queryWrapper.ge(MessageRecord::getCreatedAt, createdAtStart);
        }
        
        if (createdAtEnd != null) {
            queryWrapper.le(MessageRecord::getCreatedAt, createdAtEnd);
        }
        
        queryWrapper.orderByDesc(MessageRecord::getCreatedAt);
        return queryWrapper;
    }
}
