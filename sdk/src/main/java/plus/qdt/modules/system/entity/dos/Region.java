package plus.qdt.modules.system.entity.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 行政地区
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("qdt_region")
@Schema(title = "行政地区")
public class Region {

    @TableId(value = "code")
    @Schema(title = "行政区划代码")
    private String id;

    @NotEmpty(message = "父id不能为空")
    @Schema(title = "父id")
    @TableField(value = "parent_code")
    private String parentId;

    @Schema(title = "省代码")
    private String provinceCode;

    @Schema(title = "省名称")
    @TableField(exist = false)
    private String provinceName;

    @Schema(title = "城市代码")
    private String cityCode;

    @Schema(title = "城市名称")
    @TableField(exist = false)
    private String cityName;

    @Schema(title = "区县代码")
    private String areaCode;

    @Schema(title = "乡镇代码")
    private String streetCode;

    @NotEmpty(message = "区域中心点经纬度不能为空")
    @Schema(title = "区域中心点经纬度")
    private String center;

    @Schema(title = "行政区划级别", description = "country:国家,  province:省, city:市, district:区县, street:乡/镇/街道, village:村")
    @NotEmpty(message = "行政区划级别不能为空")
    private String level;

    @NotEmpty(message = "名称不能为空")
    @Schema(title = "名称")
    private String name;

    @NotNull(message = "排序不能为空")
    @Schema(title = "排序")
    private Integer orderNum;

    @JsonIgnore
    @TableField(exist = false)
    @Schema(title = "拼音", hidden = true)
    private String pinyin;
}