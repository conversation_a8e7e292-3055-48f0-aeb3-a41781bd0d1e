package plus.qdt.modules.system.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * 云中鹤地址接口响应DTO
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
public class YzhAddressResponseDTO {

    /**
     * 接口成功与否标识
     */
    private Boolean success;

    /**
     * 接口业务编码
     */
    private String code;

    /**
     * 接口业务描述
     */
    private String desc;

    /**
     * 接口业务数据对象
     */
    private List<YzhAddressItemDTO> result;

    /**
     * 接口响应的时间戳
     */
    private Long time;

    /**
     * 云中鹤地址项DTO
     */
    @Data
    public static class YzhAddressItemDTO {

        /**
         * 地址编码
         */
        private String addressCode;

        /**
         * 地址名称
         */
        private String addressName;

        /**
         * 上级地址编码
         */
        private String parentAddressCode;

        /**
         * 地址等级
         * 1001:一级地址(省)
         * 1002:二级地址(市)
         * 1003:三级地址(区)
         * 1004:四级地址(乡镇街道)
         */
        private Integer addressLevel;

        /**
         * 转换为通用地址DTO
         */
        public ThirdPartyAddressDTO toThirdPartyAddressDTO() {
            ThirdPartyAddressDTO dto = new ThirdPartyAddressDTO();
            dto.setAddressCode(this.addressCode);
            dto.setAddressName(this.addressName);
            dto.setAddressLevel(this.addressLevel);
            dto.setParentCode(this.parentAddressCode);
            dto.setSupplierType("YZH");
            dto.setEnabled(true);
            dto.setSort(0);
            return dto;
        }
    }
}
