package plus.qdt.modules.payment.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.payment.entity.dos.CombinePaymentLog;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 合单支付查询参数
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @Description:
 * @since 2023/6/30 15:04
 */
@Data
public class CombineSearchParams extends PageVO {

    @Schema(title = "合单支付号码")
    private String orderSn;

    @Schema(title = "支付客户端")
    private String paymentClient;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "客户端类型")
    private String clientType;

    @Schema(title = "回查确认状态")
    private Boolean isCheck;

    @Schema(title = "支付发起开始时间")
    private String startDate;

    @Schema(title = "支付发起结束时间")
    private String endDate;


    public LambdaQueryWrapper<CombinePaymentLog> generateWrapper() {

        LambdaQueryWrapper<CombinePaymentLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(CharSequenceUtil.isNotEmpty(this.orderSn), CombinePaymentLog::getOrderSn, this.orderSn);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(this.paymentClient), CombinePaymentLog::getPaymentClient, this.paymentClient);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(this.paymentMethod), CombinePaymentLog::getPaymentMethod, this.paymentMethod);
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(this.clientType), CombinePaymentLog::getPaymentClient, this.clientType);
        queryWrapper.eq(this.isCheck != null, CombinePaymentLog::getIsCheck, this.isCheck);
        queryWrapper.between(this.startDate != null && endDate != null, CombinePaymentLog::getCreateTime, this.startDate, this.endDate);
        return queryWrapper;

    }
}
