package plus.qdt.modules.payment.entity.dos;

import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 支付补差
 *
 * <AUTHOR>
 * @since 2021/1/28 09:21
 */
@Data
@TableName("li_payment_subsidies")
@Builder
@Schema(title = "支付补差")
@NoArgsConstructor
@AllArgsConstructor
public class PaymentSubsidies extends BaseEntity {


    @Schema(title = "付款人ID")
    private String payerId;

    @Schema(title = "付款人名称")
    private String nickname;

    //收款人信息，如果是-1，则标识平台收款
    @Schema(title = "收款人ID")
    private String payeeId;

    @Schema(title = "订单编号")
    private String orderSn;

    @Schema(title = "订单描述")
    private String description;

    /**
     * @see plus.qdt.modules.payment.entity.enums.PaymentClientEnum
     */
    @Schema(title = "订单来源")
    private String paymentClient;

    /**
     * @see plus.qdt.modules.payment.entity.enums.PaymentMethodEnum
     */
    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "金额")
    private Double price;


    @Schema(title = "第三方支付发起交易号")
    private String outTradeNo;

    @Schema(title = "第三方平台付款流水号")
    private String transactionId;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "支付过期时间", hidden = true)
    private Date timeoutExpress;

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;


    //保留，以下内容暂时用不到，目前补差逻辑全部使用平台内部的余额支付
    @Schema(title = "支付请求参数")
    private String paymentRequestSource;


    @Schema(title = "支付响应")
    private String paymentResponseSource;

    @Schema(title = "分账单号")
    private String outOrderNo;

    @Schema(title = "分账详细")
    private String receivers;

    /**
     * 生成补差描述
     *
     * @param orderSn 订单号
     * @param amount  补差金额
     * @return 补差描述
     */
    public static String generateSubsidiesText(String orderSn, Double amount) {
        return String.format("订单[%s]补差,金额[%f]", orderSn, amount);
    }

}
