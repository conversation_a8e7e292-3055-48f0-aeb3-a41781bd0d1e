package plus.qdt.modules.member.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户编辑DTO
 *
 * <AUTHOR> @ gmail.com)
 * @version v4.0
 * @since 2023/4/24 10:16
 */

@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserEditBaseDTO {

    private String id;

    @Size(min = 2, max = 12, message = "昵称长度必须在2-12之间")
    private String nickName;

    @Min(value = -1, message = "性别只能为-1,0,1")
    @Max(value = 1, message = "性别只能为-1,0,1")
    private Integer sex;

    @Size(max = 255, message = "头像图片地址长度不能超过255位")
    private String face;

    @Getter
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "生日")
    private Date birthday;

    @Schema(title = "地区编码")
    private String areaCode;

}
