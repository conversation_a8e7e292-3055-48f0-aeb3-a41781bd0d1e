package plus.qdt.modules.member.entity.enums;

/**
 * <AUTHOR>
 * @since 2025-07-02
 */
public enum ReceiptTypeEnum {
    /**
     * 发票类型
         * @param null
     * @return void
     * <AUTHOR>
     */

    USER("用户发票"), STORE("商户发票"),SUPPLIER("供应商发票");
    private String description;

    ReceiptTypeEnum(String str) {
        this.description = str;

    }

    public String description() {
        return description;
    }


}
