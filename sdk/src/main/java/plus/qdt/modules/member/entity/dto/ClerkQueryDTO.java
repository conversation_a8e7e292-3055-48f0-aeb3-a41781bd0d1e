package plus.qdt.modules.member.entity.dto;

import plus.qdt.mybatis.model.BaseSceneEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * 店员查询
 *
 * <AUTHOR>
 * @since 2020/11/16 19:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "店员查询")
public class ClerkQueryDTO extends BaseSceneEntity {

    private static final long serialVersionUID = 1L;

    @Schema(title = "用户名")
    @Length(max = 20, message = "用户名长度不能超过20个字符")
    private String username;

    @Schema(title = "手机")
    @Length(max = 11, message = "手机号长度不能超过11")
    private String mobile;

    @Schema(title = "所属部门id")
    private String departmentId;

    @Schema(title = "是否为超级管理员")
    private Boolean isSuper;

    @Schema(title = "状态")
    private Boolean status;

    @Schema(title = "店铺id", hidden = true)
    private String storeId;
    @Schema(title = "查询场景", hidden = true)
    private String scene;

}
