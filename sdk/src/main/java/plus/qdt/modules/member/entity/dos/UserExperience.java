package plus.qdt.modules.member.entity.dos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserExperience extends BaseStandardEntity {

    @Schema(title = "消费之前经验值")
    private Long beforeExperience;

    @Schema(title = "当前经验值")
    private Long experience;

    @Schema(title = "内容")
    private String content;

    @Schema(title = "VIP用户")
    private String userId;

    @Schema(title = "会员过期时间")
    private Date premiumGradeExpireTime;

    @Schema(title = "会员等级ID")
    private Long premiumGradeId;

}
