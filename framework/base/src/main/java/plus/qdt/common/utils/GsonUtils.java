package plus.qdt.common.utils;

import com.google.gson.GsonBuilder;

public class GsonUtils {

    private GsonUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static String toJson(Object object) {
        return new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create().toJson(object);
    }

    public static <T> T fromJson(String json, Class<T> classOfT) {
        return new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create().fromJson(json, classOfT);
    }

}
