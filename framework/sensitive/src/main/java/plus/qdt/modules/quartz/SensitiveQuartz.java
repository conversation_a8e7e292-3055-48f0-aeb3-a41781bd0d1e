package plus.qdt.modules.quartz;

import plus.qdt.cache.Cache;
import plus.qdt.cache.CachePrefix;
import plus.qdt.modules.sensitive.SensitiveWordsFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.List;

/**
 * 间隔更新敏感词
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-11-23 16:31
 */
@Slf4j
@RequiredArgsConstructor
public class SensitiveQuartz extends QuartzJobBean {

    private final Cache<List<String>> cache;

    /**
     * 定时更新敏感词信息
     *
     * @param jobExecutionContext
     */
    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) {
        log.info("敏感词定时更新");
        List<String> sensitives = cache.get(CachePrefix.SENSITIVE.getPrefix());
        if (sensitives == null || sensitives.isEmpty()) {
            return;
        }
        SensitiveWordsFilter.init(sensitives);
    }
}